package com.example.abonekaptanmobile.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Float;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class StageAnalysisDao_Impl implements StageAnalysisDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<StageAnalysisEntity> __insertionAdapterOfStageAnalysisEntity;

  private final SharedSQLiteStatement __preparedStmtOfUpdateStage2Results;

  private final SharedSQLiteStatement __preparedStmtOfUpdateProcessingStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllAnalysisResults;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldAnalysisResults;

  public StageAnalysisDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfStageAnalysisEntity = new EntityInsertionAdapter<StageAnalysisEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `stage_analysis` (`id`,`emailIndex`,`emailId`,`domain`,`subject`,`from`,`date`,`stage1_isSubscriptionCompany`,`stage1_companyName`,`stage1_companyConfidence`,`stage1_timestamp`,`stage2_emailType`,`stage2_emailTypeConfidence`,`stage2_timestamp`,`stage2_rawContent`,`processingStatus`,`createdAt`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final StageAnalysisEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getEmailIndex());
        if (entity.getEmailId() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getEmailId());
        }
        if (entity.getDomain() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDomain());
        }
        if (entity.getSubject() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSubject());
        }
        if (entity.getFrom() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getFrom());
        }
        statement.bindLong(7, entity.getDate());
        final int _tmp = entity.getStage1_isSubscriptionCompany() ? 1 : 0;
        statement.bindLong(8, _tmp);
        if (entity.getStage1_companyName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getStage1_companyName());
        }
        statement.bindDouble(10, entity.getStage1_companyConfidence());
        statement.bindLong(11, entity.getStage1_timestamp());
        if (entity.getStage2_emailType() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getStage2_emailType());
        }
        if (entity.getStage2_emailTypeConfidence() == null) {
          statement.bindNull(13);
        } else {
          statement.bindDouble(13, entity.getStage2_emailTypeConfidence());
        }
        if (entity.getStage2_timestamp() == null) {
          statement.bindNull(14);
        } else {
          statement.bindLong(14, entity.getStage2_timestamp());
        }
        if (entity.getStage2_rawContent() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getStage2_rawContent());
        }
        if (entity.getProcessingStatus() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getProcessingStatus());
        }
        statement.bindLong(17, entity.getCreatedAt());
      }
    };
    this.__preparedStmtOfUpdateStage2Results = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "\n"
                + "        UPDATE stage_analysis \n"
                + "        SET stage2_emailType = ?,\n"
                + "            stage2_emailTypeConfidence = ?,\n"
                + "            stage2_timestamp = ?,\n"
                + "            stage2_rawContent = ?,\n"
                + "            processingStatus = 'STAGE2_COMPLETED'\n"
                + "        WHERE id = ?\n"
                + "    ";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateProcessingStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE stage_analysis SET processingStatus = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllAnalysisResults = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM stage_analysis";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldAnalysisResults = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM stage_analysis WHERE createdAt < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertAnalysisResult(final StageAnalysisEntity result,
      final Continuation<? super Long> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfStageAnalysisEntity.insertAndReturnId(result);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object insertAnalysisResults(final List<StageAnalysisEntity> results,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfStageAnalysisEntity.insert(results);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateStage2Results(final long id, final String emailType, final float confidence,
      final long timestamp, final String rawContent, final Continuation<? super Unit> arg5) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateStage2Results.acquire();
        int _argIndex = 1;
        if (emailType == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, emailType);
        }
        _argIndex = 2;
        _stmt.bindDouble(_argIndex, confidence);
        _argIndex = 3;
        _stmt.bindLong(_argIndex, timestamp);
        _argIndex = 4;
        if (rawContent == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, rawContent);
        }
        _argIndex = 5;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateStage2Results.release(_stmt);
        }
      }
    }, arg5);
  }

  @Override
  public Object updateProcessingStatus(final long id, final String status,
      final Continuation<? super Unit> arg2) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateProcessingStatus.acquire();
        int _argIndex = 1;
        if (status == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, status);
        }
        _argIndex = 2;
        _stmt.bindLong(_argIndex, id);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateProcessingStatus.release(_stmt);
        }
      }
    }, arg2);
  }

  @Override
  public Object deleteAllAnalysisResults(final Continuation<? super Unit> arg0) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllAnalysisResults.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllAnalysisResults.release(_stmt);
        }
      }
    }, arg0);
  }

  @Override
  public Object deleteOldAnalysisResults(final long timestamp,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldAnalysisResults.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldAnalysisResults.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Flow<List<StageAnalysisEntity>> getAllAnalysisResults() {
    final String _sql = "SELECT * FROM stage_analysis ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"stage_analysis"}, new Callable<List<StageAnalysisEntity>>() {
      @Override
      @NonNull
      public List<StageAnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfEmailId = CursorUtil.getColumnIndexOrThrow(_cursor, "emailId");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStage1IsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_isSubscriptionCompany");
          final int _cursorIndexOfStage1CompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyName");
          final int _cursorIndexOfStage1CompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyConfidence");
          final int _cursorIndexOfStage1Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_timestamp");
          final int _cursorIndexOfStage2EmailType = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailType");
          final int _cursorIndexOfStage2EmailTypeConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailTypeConfidence");
          final int _cursorIndexOfStage2Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_timestamp");
          final int _cursorIndexOfStage2RawContent = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_rawContent");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<StageAnalysisEntity> _result = new ArrayList<StageAnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StageAnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpEmailId;
            if (_cursor.isNull(_cursorIndexOfEmailId)) {
              _tmpEmailId = null;
            } else {
              _tmpEmailId = _cursor.getString(_cursorIndexOfEmailId);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final boolean _tmpStage1_isSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfStage1IsSubscriptionCompany);
            _tmpStage1_isSubscriptionCompany = _tmp != 0;
            final String _tmpStage1_companyName;
            if (_cursor.isNull(_cursorIndexOfStage1CompanyName)) {
              _tmpStage1_companyName = null;
            } else {
              _tmpStage1_companyName = _cursor.getString(_cursorIndexOfStage1CompanyName);
            }
            final float _tmpStage1_companyConfidence;
            _tmpStage1_companyConfidence = _cursor.getFloat(_cursorIndexOfStage1CompanyConfidence);
            final long _tmpStage1_timestamp;
            _tmpStage1_timestamp = _cursor.getLong(_cursorIndexOfStage1Timestamp);
            final String _tmpStage2_emailType;
            if (_cursor.isNull(_cursorIndexOfStage2EmailType)) {
              _tmpStage2_emailType = null;
            } else {
              _tmpStage2_emailType = _cursor.getString(_cursorIndexOfStage2EmailType);
            }
            final Float _tmpStage2_emailTypeConfidence;
            if (_cursor.isNull(_cursorIndexOfStage2EmailTypeConfidence)) {
              _tmpStage2_emailTypeConfidence = null;
            } else {
              _tmpStage2_emailTypeConfidence = _cursor.getFloat(_cursorIndexOfStage2EmailTypeConfidence);
            }
            final Long _tmpStage2_timestamp;
            if (_cursor.isNull(_cursorIndexOfStage2Timestamp)) {
              _tmpStage2_timestamp = null;
            } else {
              _tmpStage2_timestamp = _cursor.getLong(_cursorIndexOfStage2Timestamp);
            }
            final String _tmpStage2_rawContent;
            if (_cursor.isNull(_cursorIndexOfStage2RawContent)) {
              _tmpStage2_rawContent = null;
            } else {
              _tmpStage2_rawContent = _cursor.getString(_cursorIndexOfStage2RawContent);
            }
            final String _tmpProcessingStatus;
            if (_cursor.isNull(_cursorIndexOfProcessingStatus)) {
              _tmpProcessingStatus = null;
            } else {
              _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new StageAnalysisEntity(_tmpId,_tmpEmailIndex,_tmpEmailId,_tmpDomain,_tmpSubject,_tmpFrom,_tmpDate,_tmpStage1_isSubscriptionCompany,_tmpStage1_companyName,_tmpStage1_companyConfidence,_tmpStage1_timestamp,_tmpStage2_emailType,_tmpStage2_emailTypeConfidence,_tmpStage2_timestamp,_tmpStage2_rawContent,_tmpProcessingStatus,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<StageAnalysisEntity>> getStage1CompletedEmails(final float minConfidence) {
    final String _sql = "SELECT * FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 AND stage1_companyConfidence >= ? ORDER BY date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minConfidence);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"stage_analysis"}, new Callable<List<StageAnalysisEntity>>() {
      @Override
      @NonNull
      public List<StageAnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfEmailId = CursorUtil.getColumnIndexOrThrow(_cursor, "emailId");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStage1IsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_isSubscriptionCompany");
          final int _cursorIndexOfStage1CompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyName");
          final int _cursorIndexOfStage1CompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyConfidence");
          final int _cursorIndexOfStage1Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_timestamp");
          final int _cursorIndexOfStage2EmailType = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailType");
          final int _cursorIndexOfStage2EmailTypeConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailTypeConfidence");
          final int _cursorIndexOfStage2Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_timestamp");
          final int _cursorIndexOfStage2RawContent = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_rawContent");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<StageAnalysisEntity> _result = new ArrayList<StageAnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StageAnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpEmailId;
            if (_cursor.isNull(_cursorIndexOfEmailId)) {
              _tmpEmailId = null;
            } else {
              _tmpEmailId = _cursor.getString(_cursorIndexOfEmailId);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final boolean _tmpStage1_isSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfStage1IsSubscriptionCompany);
            _tmpStage1_isSubscriptionCompany = _tmp != 0;
            final String _tmpStage1_companyName;
            if (_cursor.isNull(_cursorIndexOfStage1CompanyName)) {
              _tmpStage1_companyName = null;
            } else {
              _tmpStage1_companyName = _cursor.getString(_cursorIndexOfStage1CompanyName);
            }
            final float _tmpStage1_companyConfidence;
            _tmpStage1_companyConfidence = _cursor.getFloat(_cursorIndexOfStage1CompanyConfidence);
            final long _tmpStage1_timestamp;
            _tmpStage1_timestamp = _cursor.getLong(_cursorIndexOfStage1Timestamp);
            final String _tmpStage2_emailType;
            if (_cursor.isNull(_cursorIndexOfStage2EmailType)) {
              _tmpStage2_emailType = null;
            } else {
              _tmpStage2_emailType = _cursor.getString(_cursorIndexOfStage2EmailType);
            }
            final Float _tmpStage2_emailTypeConfidence;
            if (_cursor.isNull(_cursorIndexOfStage2EmailTypeConfidence)) {
              _tmpStage2_emailTypeConfidence = null;
            } else {
              _tmpStage2_emailTypeConfidence = _cursor.getFloat(_cursorIndexOfStage2EmailTypeConfidence);
            }
            final Long _tmpStage2_timestamp;
            if (_cursor.isNull(_cursorIndexOfStage2Timestamp)) {
              _tmpStage2_timestamp = null;
            } else {
              _tmpStage2_timestamp = _cursor.getLong(_cursorIndexOfStage2Timestamp);
            }
            final String _tmpStage2_rawContent;
            if (_cursor.isNull(_cursorIndexOfStage2RawContent)) {
              _tmpStage2_rawContent = null;
            } else {
              _tmpStage2_rawContent = _cursor.getString(_cursorIndexOfStage2RawContent);
            }
            final String _tmpProcessingStatus;
            if (_cursor.isNull(_cursorIndexOfProcessingStatus)) {
              _tmpProcessingStatus = null;
            } else {
              _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new StageAnalysisEntity(_tmpId,_tmpEmailIndex,_tmpEmailId,_tmpDomain,_tmpSubject,_tmpFrom,_tmpDate,_tmpStage1_isSubscriptionCompany,_tmpStage1_companyName,_tmpStage1_companyConfidence,_tmpStage1_timestamp,_tmpStage2_emailType,_tmpStage2_emailTypeConfidence,_tmpStage2_timestamp,_tmpStage2_rawContent,_tmpProcessingStatus,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<StageAnalysisEntity>> getEmailsForStage2(final float minConfidence) {
    final String _sql = "SELECT * FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 AND stage1_companyConfidence >= ? AND processingStatus = 'STAGE1_COMPLETED' ORDER BY date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindDouble(_argIndex, minConfidence);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"stage_analysis"}, new Callable<List<StageAnalysisEntity>>() {
      @Override
      @NonNull
      public List<StageAnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfEmailId = CursorUtil.getColumnIndexOrThrow(_cursor, "emailId");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStage1IsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_isSubscriptionCompany");
          final int _cursorIndexOfStage1CompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyName");
          final int _cursorIndexOfStage1CompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyConfidence");
          final int _cursorIndexOfStage1Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_timestamp");
          final int _cursorIndexOfStage2EmailType = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailType");
          final int _cursorIndexOfStage2EmailTypeConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailTypeConfidence");
          final int _cursorIndexOfStage2Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_timestamp");
          final int _cursorIndexOfStage2RawContent = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_rawContent");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<StageAnalysisEntity> _result = new ArrayList<StageAnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StageAnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpEmailId;
            if (_cursor.isNull(_cursorIndexOfEmailId)) {
              _tmpEmailId = null;
            } else {
              _tmpEmailId = _cursor.getString(_cursorIndexOfEmailId);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final boolean _tmpStage1_isSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfStage1IsSubscriptionCompany);
            _tmpStage1_isSubscriptionCompany = _tmp != 0;
            final String _tmpStage1_companyName;
            if (_cursor.isNull(_cursorIndexOfStage1CompanyName)) {
              _tmpStage1_companyName = null;
            } else {
              _tmpStage1_companyName = _cursor.getString(_cursorIndexOfStage1CompanyName);
            }
            final float _tmpStage1_companyConfidence;
            _tmpStage1_companyConfidence = _cursor.getFloat(_cursorIndexOfStage1CompanyConfidence);
            final long _tmpStage1_timestamp;
            _tmpStage1_timestamp = _cursor.getLong(_cursorIndexOfStage1Timestamp);
            final String _tmpStage2_emailType;
            if (_cursor.isNull(_cursorIndexOfStage2EmailType)) {
              _tmpStage2_emailType = null;
            } else {
              _tmpStage2_emailType = _cursor.getString(_cursorIndexOfStage2EmailType);
            }
            final Float _tmpStage2_emailTypeConfidence;
            if (_cursor.isNull(_cursorIndexOfStage2EmailTypeConfidence)) {
              _tmpStage2_emailTypeConfidence = null;
            } else {
              _tmpStage2_emailTypeConfidence = _cursor.getFloat(_cursorIndexOfStage2EmailTypeConfidence);
            }
            final Long _tmpStage2_timestamp;
            if (_cursor.isNull(_cursorIndexOfStage2Timestamp)) {
              _tmpStage2_timestamp = null;
            } else {
              _tmpStage2_timestamp = _cursor.getLong(_cursorIndexOfStage2Timestamp);
            }
            final String _tmpStage2_rawContent;
            if (_cursor.isNull(_cursorIndexOfStage2RawContent)) {
              _tmpStage2_rawContent = null;
            } else {
              _tmpStage2_rawContent = _cursor.getString(_cursorIndexOfStage2RawContent);
            }
            final String _tmpProcessingStatus;
            if (_cursor.isNull(_cursorIndexOfProcessingStatus)) {
              _tmpProcessingStatus = null;
            } else {
              _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new StageAnalysisEntity(_tmpId,_tmpEmailIndex,_tmpEmailId,_tmpDomain,_tmpSubject,_tmpFrom,_tmpDate,_tmpStage1_isSubscriptionCompany,_tmpStage1_companyName,_tmpStage1_companyConfidence,_tmpStage1_timestamp,_tmpStage2_emailType,_tmpStage2_emailTypeConfidence,_tmpStage2_timestamp,_tmpStage2_rawContent,_tmpProcessingStatus,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<StageAnalysisEntity>> getStage2CompletedEmails() {
    final String _sql = "SELECT * FROM stage_analysis WHERE processingStatus = 'STAGE2_COMPLETED' ORDER BY date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"stage_analysis"}, new Callable<List<StageAnalysisEntity>>() {
      @Override
      @NonNull
      public List<StageAnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfEmailId = CursorUtil.getColumnIndexOrThrow(_cursor, "emailId");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStage1IsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_isSubscriptionCompany");
          final int _cursorIndexOfStage1CompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyName");
          final int _cursorIndexOfStage1CompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyConfidence");
          final int _cursorIndexOfStage1Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_timestamp");
          final int _cursorIndexOfStage2EmailType = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailType");
          final int _cursorIndexOfStage2EmailTypeConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailTypeConfidence");
          final int _cursorIndexOfStage2Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_timestamp");
          final int _cursorIndexOfStage2RawContent = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_rawContent");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<StageAnalysisEntity> _result = new ArrayList<StageAnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StageAnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpEmailId;
            if (_cursor.isNull(_cursorIndexOfEmailId)) {
              _tmpEmailId = null;
            } else {
              _tmpEmailId = _cursor.getString(_cursorIndexOfEmailId);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final boolean _tmpStage1_isSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfStage1IsSubscriptionCompany);
            _tmpStage1_isSubscriptionCompany = _tmp != 0;
            final String _tmpStage1_companyName;
            if (_cursor.isNull(_cursorIndexOfStage1CompanyName)) {
              _tmpStage1_companyName = null;
            } else {
              _tmpStage1_companyName = _cursor.getString(_cursorIndexOfStage1CompanyName);
            }
            final float _tmpStage1_companyConfidence;
            _tmpStage1_companyConfidence = _cursor.getFloat(_cursorIndexOfStage1CompanyConfidence);
            final long _tmpStage1_timestamp;
            _tmpStage1_timestamp = _cursor.getLong(_cursorIndexOfStage1Timestamp);
            final String _tmpStage2_emailType;
            if (_cursor.isNull(_cursorIndexOfStage2EmailType)) {
              _tmpStage2_emailType = null;
            } else {
              _tmpStage2_emailType = _cursor.getString(_cursorIndexOfStage2EmailType);
            }
            final Float _tmpStage2_emailTypeConfidence;
            if (_cursor.isNull(_cursorIndexOfStage2EmailTypeConfidence)) {
              _tmpStage2_emailTypeConfidence = null;
            } else {
              _tmpStage2_emailTypeConfidence = _cursor.getFloat(_cursorIndexOfStage2EmailTypeConfidence);
            }
            final Long _tmpStage2_timestamp;
            if (_cursor.isNull(_cursorIndexOfStage2Timestamp)) {
              _tmpStage2_timestamp = null;
            } else {
              _tmpStage2_timestamp = _cursor.getLong(_cursorIndexOfStage2Timestamp);
            }
            final String _tmpStage2_rawContent;
            if (_cursor.isNull(_cursorIndexOfStage2RawContent)) {
              _tmpStage2_rawContent = null;
            } else {
              _tmpStage2_rawContent = _cursor.getString(_cursorIndexOfStage2RawContent);
            }
            final String _tmpProcessingStatus;
            if (_cursor.isNull(_cursorIndexOfProcessingStatus)) {
              _tmpProcessingStatus = null;
            } else {
              _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new StageAnalysisEntity(_tmpId,_tmpEmailIndex,_tmpEmailId,_tmpDomain,_tmpSubject,_tmpFrom,_tmpDate,_tmpStage1_isSubscriptionCompany,_tmpStage1_companyName,_tmpStage1_companyConfidence,_tmpStage1_timestamp,_tmpStage2_emailType,_tmpStage2_emailTypeConfidence,_tmpStage2_timestamp,_tmpStage2_rawContent,_tmpProcessingStatus,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<StageAnalysisEntity>> getAnalysisResultsByCompany(final String companyName) {
    final String _sql = "SELECT * FROM stage_analysis WHERE stage1_companyName = ? ORDER BY date ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (companyName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, companyName);
    }
    return CoroutinesRoom.createFlow(__db, false, new String[] {"stage_analysis"}, new Callable<List<StageAnalysisEntity>>() {
      @Override
      @NonNull
      public List<StageAnalysisEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfEmailId = CursorUtil.getColumnIndexOrThrow(_cursor, "emailId");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStage1IsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_isSubscriptionCompany");
          final int _cursorIndexOfStage1CompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyName");
          final int _cursorIndexOfStage1CompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyConfidence");
          final int _cursorIndexOfStage1Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_timestamp");
          final int _cursorIndexOfStage2EmailType = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailType");
          final int _cursorIndexOfStage2EmailTypeConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailTypeConfidence");
          final int _cursorIndexOfStage2Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_timestamp");
          final int _cursorIndexOfStage2RawContent = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_rawContent");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final List<StageAnalysisEntity> _result = new ArrayList<StageAnalysisEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final StageAnalysisEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpEmailId;
            if (_cursor.isNull(_cursorIndexOfEmailId)) {
              _tmpEmailId = null;
            } else {
              _tmpEmailId = _cursor.getString(_cursorIndexOfEmailId);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final boolean _tmpStage1_isSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfStage1IsSubscriptionCompany);
            _tmpStage1_isSubscriptionCompany = _tmp != 0;
            final String _tmpStage1_companyName;
            if (_cursor.isNull(_cursorIndexOfStage1CompanyName)) {
              _tmpStage1_companyName = null;
            } else {
              _tmpStage1_companyName = _cursor.getString(_cursorIndexOfStage1CompanyName);
            }
            final float _tmpStage1_companyConfidence;
            _tmpStage1_companyConfidence = _cursor.getFloat(_cursorIndexOfStage1CompanyConfidence);
            final long _tmpStage1_timestamp;
            _tmpStage1_timestamp = _cursor.getLong(_cursorIndexOfStage1Timestamp);
            final String _tmpStage2_emailType;
            if (_cursor.isNull(_cursorIndexOfStage2EmailType)) {
              _tmpStage2_emailType = null;
            } else {
              _tmpStage2_emailType = _cursor.getString(_cursorIndexOfStage2EmailType);
            }
            final Float _tmpStage2_emailTypeConfidence;
            if (_cursor.isNull(_cursorIndexOfStage2EmailTypeConfidence)) {
              _tmpStage2_emailTypeConfidence = null;
            } else {
              _tmpStage2_emailTypeConfidence = _cursor.getFloat(_cursorIndexOfStage2EmailTypeConfidence);
            }
            final Long _tmpStage2_timestamp;
            if (_cursor.isNull(_cursorIndexOfStage2Timestamp)) {
              _tmpStage2_timestamp = null;
            } else {
              _tmpStage2_timestamp = _cursor.getLong(_cursorIndexOfStage2Timestamp);
            }
            final String _tmpStage2_rawContent;
            if (_cursor.isNull(_cursorIndexOfStage2RawContent)) {
              _tmpStage2_rawContent = null;
            } else {
              _tmpStage2_rawContent = _cursor.getString(_cursorIndexOfStage2RawContent);
            }
            final String _tmpProcessingStatus;
            if (_cursor.isNull(_cursorIndexOfProcessingStatus)) {
              _tmpProcessingStatus = null;
            } else {
              _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _item = new StageAnalysisEntity(_tmpId,_tmpEmailIndex,_tmpEmailId,_tmpDomain,_tmpSubject,_tmpFrom,_tmpDate,_tmpStage1_isSubscriptionCompany,_tmpStage1_companyName,_tmpStage1_companyConfidence,_tmpStage1_timestamp,_tmpStage2_emailType,_tmpStage2_emailTypeConfidence,_tmpStage2_timestamp,_tmpStage2_rawContent,_tmpProcessingStatus,_tmpCreatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getAnalysisResultByEmailId(final String emailId,
      final Continuation<? super StageAnalysisEntity> arg1) {
    final String _sql = "SELECT * FROM stage_analysis WHERE emailId = ? LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (emailId == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, emailId);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<StageAnalysisEntity>() {
      @Override
      @Nullable
      public StageAnalysisEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfEmailId = CursorUtil.getColumnIndexOrThrow(_cursor, "emailId");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfFrom = CursorUtil.getColumnIndexOrThrow(_cursor, "from");
          final int _cursorIndexOfDate = CursorUtil.getColumnIndexOrThrow(_cursor, "date");
          final int _cursorIndexOfStage1IsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_isSubscriptionCompany");
          final int _cursorIndexOfStage1CompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyName");
          final int _cursorIndexOfStage1CompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_companyConfidence");
          final int _cursorIndexOfStage1Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage1_timestamp");
          final int _cursorIndexOfStage2EmailType = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailType");
          final int _cursorIndexOfStage2EmailTypeConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_emailTypeConfidence");
          final int _cursorIndexOfStage2Timestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_timestamp");
          final int _cursorIndexOfStage2RawContent = CursorUtil.getColumnIndexOrThrow(_cursor, "stage2_rawContent");
          final int _cursorIndexOfProcessingStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "processingStatus");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final StageAnalysisEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpEmailId;
            if (_cursor.isNull(_cursorIndexOfEmailId)) {
              _tmpEmailId = null;
            } else {
              _tmpEmailId = _cursor.getString(_cursorIndexOfEmailId);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpFrom;
            if (_cursor.isNull(_cursorIndexOfFrom)) {
              _tmpFrom = null;
            } else {
              _tmpFrom = _cursor.getString(_cursorIndexOfFrom);
            }
            final long _tmpDate;
            _tmpDate = _cursor.getLong(_cursorIndexOfDate);
            final boolean _tmpStage1_isSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfStage1IsSubscriptionCompany);
            _tmpStage1_isSubscriptionCompany = _tmp != 0;
            final String _tmpStage1_companyName;
            if (_cursor.isNull(_cursorIndexOfStage1CompanyName)) {
              _tmpStage1_companyName = null;
            } else {
              _tmpStage1_companyName = _cursor.getString(_cursorIndexOfStage1CompanyName);
            }
            final float _tmpStage1_companyConfidence;
            _tmpStage1_companyConfidence = _cursor.getFloat(_cursorIndexOfStage1CompanyConfidence);
            final long _tmpStage1_timestamp;
            _tmpStage1_timestamp = _cursor.getLong(_cursorIndexOfStage1Timestamp);
            final String _tmpStage2_emailType;
            if (_cursor.isNull(_cursorIndexOfStage2EmailType)) {
              _tmpStage2_emailType = null;
            } else {
              _tmpStage2_emailType = _cursor.getString(_cursorIndexOfStage2EmailType);
            }
            final Float _tmpStage2_emailTypeConfidence;
            if (_cursor.isNull(_cursorIndexOfStage2EmailTypeConfidence)) {
              _tmpStage2_emailTypeConfidence = null;
            } else {
              _tmpStage2_emailTypeConfidence = _cursor.getFloat(_cursorIndexOfStage2EmailTypeConfidence);
            }
            final Long _tmpStage2_timestamp;
            if (_cursor.isNull(_cursorIndexOfStage2Timestamp)) {
              _tmpStage2_timestamp = null;
            } else {
              _tmpStage2_timestamp = _cursor.getLong(_cursorIndexOfStage2Timestamp);
            }
            final String _tmpStage2_rawContent;
            if (_cursor.isNull(_cursorIndexOfStage2RawContent)) {
              _tmpStage2_rawContent = null;
            } else {
              _tmpStage2_rawContent = _cursor.getString(_cursorIndexOfStage2RawContent);
            }
            final String _tmpProcessingStatus;
            if (_cursor.isNull(_cursorIndexOfProcessingStatus)) {
              _tmpProcessingStatus = null;
            } else {
              _tmpProcessingStatus = _cursor.getString(_cursorIndexOfProcessingStatus);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            _result = new StageAnalysisEntity(_tmpId,_tmpEmailIndex,_tmpEmailId,_tmpDomain,_tmpSubject,_tmpFrom,_tmpDate,_tmpStage1_isSubscriptionCompany,_tmpStage1_companyName,_tmpStage1_companyConfidence,_tmpStage1_timestamp,_tmpStage2_emailType,_tmpStage2_emailTypeConfidence,_tmpStage2_timestamp,_tmpStage2_rawContent,_tmpProcessingStatus,_tmpCreatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getAnalysisStatistics(final Continuation<? super AnalysisStatistics> arg0) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            COUNT(*) as totalEmails,\n"
            + "            SUM(CASE WHEN stage1_isSubscriptionCompany = 1 THEN 1 ELSE 0 END) as stage1Passed,\n"
            + "            SUM(CASE WHEN processingStatus = 'STAGE2_COMPLETED' THEN 1 ELSE 0 END) as stage2Completed,\n"
            + "            AVG(stage1_companyConfidence) as avgConfidence\n"
            + "        FROM stage_analysis\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AnalysisStatistics>() {
      @Override
      @NonNull
      public AnalysisStatistics call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfTotalEmails = 0;
          final int _cursorIndexOfStage1Passed = 1;
          final int _cursorIndexOfStage2Completed = 2;
          final int _cursorIndexOfAvgConfidence = 3;
          final AnalysisStatistics _result;
          if (_cursor.moveToFirst()) {
            final int _tmpTotalEmails;
            _tmpTotalEmails = _cursor.getInt(_cursorIndexOfTotalEmails);
            final int _tmpStage1Passed;
            _tmpStage1Passed = _cursor.getInt(_cursorIndexOfStage1Passed);
            final int _tmpStage2Completed;
            _tmpStage2Completed = _cursor.getInt(_cursorIndexOfStage2Completed);
            final double _tmpAvgConfidence;
            _tmpAvgConfidence = _cursor.getDouble(_cursorIndexOfAvgConfidence);
            _result = new AnalysisStatistics(_tmpTotalEmails,_tmpStage1Passed,_tmpStage2Completed,_tmpAvgConfidence);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getCompanyStatistics(final Continuation<? super List<CompanyStatistics>> arg0) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            stage1_companyName as companyName,\n"
            + "            COUNT(*) as emailCount,\n"
            + "            AVG(stage1_companyConfidence) as avgConfidence,\n"
            + "            SUM(CASE WHEN processingStatus = 'STAGE2_COMPLETED' THEN 1 ELSE 0 END) as completedCount\n"
            + "        FROM stage_analysis \n"
            + "        WHERE stage1_isSubscriptionCompany = 1 \n"
            + "        GROUP BY stage1_companyName \n"
            + "        ORDER BY emailCount DESC\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<CompanyStatistics>>() {
      @Override
      @NonNull
      public List<CompanyStatistics> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfCompanyName = 0;
          final int _cursorIndexOfEmailCount = 1;
          final int _cursorIndexOfAvgConfidence = 2;
          final int _cursorIndexOfCompletedCount = 3;
          final List<CompanyStatistics> _result = new ArrayList<CompanyStatistics>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final CompanyStatistics _item;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final int _tmpEmailCount;
            _tmpEmailCount = _cursor.getInt(_cursorIndexOfEmailCount);
            final double _tmpAvgConfidence;
            _tmpAvgConfidence = _cursor.getDouble(_cursorIndexOfAvgConfidence);
            final int _tmpCompletedCount;
            _tmpCompletedCount = _cursor.getInt(_cursorIndexOfCompletedCount);
            _item = new CompanyStatistics(_tmpCompanyName,_tmpEmailCount,_tmpAvgConfidence,_tmpCompletedCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
