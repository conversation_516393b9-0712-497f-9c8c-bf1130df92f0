// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao;
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideStageAnalysisRepositoryFactory implements Factory<StageAnalysisRepository> {
  private final Provider<StageAnalysisDao> daoProvider;

  public AppModule_ProvideStageAnalysisRepositoryFactory(Provider<StageAnalysisDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public StageAnalysisRepository get() {
    return provideStageAnalysisRepository(daoProvider.get());
  }

  public static AppModule_ProvideStageAnalysisRepositoryFactory create(
      Provider<StageAnalysisDao> daoProvider) {
    return new AppModule_ProvideStageAnalysisRepositoryFactory(daoProvider);
  }

  public static StageAnalysisRepository provideStageAnalysisRepository(StageAnalysisDao dao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideStageAnalysisRepository(dao));
  }
}
