package com.example.abonekaptanmobile.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Turkish: Abonelik veritabanı entity'si.
 * English: Subscription database entity.
 */
@Entity(tableName = "subscriptions")
data class SubscriptionEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val serviceName: String,
    val status: String, // ACTIVE, CANCELLED, UNKNOWN
    val emailCount: Int,
    val lastEmailDate: Long,
    val cancellationDate: Long? = null,
    val subscriptionStartDate: Long? = null,
    val relatedEmailIds: String // Comma-separated list of email IDs
)

/**
 * Turkish: İki aşamalı analiz sonuçları için veritabanı entity'si.
 * English: Database entity for two-stage analysis results.
 */
@Entity(tableName = "stage_analysis")
data class StageAnalysisEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val emailIndex: Int,
    val emailId: String,
    val domain: String,
    val subject: String,
    val from: String,
    val date: Long,

    // Aşama 1 sonuçları
    val stage1_isSubscriptionCompany: Boolean,
    val stage1_companyName: String,
    val stage1_companyConfidence: Float,
    val stage1_timestamp: Long,

    // Aşama 2 sonuçları (nullable - henüz analiz edilmemiş olabilir)
    val stage2_emailType: String? = null,
    val stage2_emailTypeConfidence: Float? = null,
    val stage2_timestamp: Long? = null,
    val stage2_rawContent: String? = null,

    // Genel bilgiler
    val processingStatus: String, // STAGE1_COMPLETED, STAGE2_COMPLETED, FAILED
    val createdAt: Long = System.currentTimeMillis()
)
