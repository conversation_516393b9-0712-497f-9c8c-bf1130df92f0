Aapp/src/main/java/com/example/abonekaptanmobile/AboneKaptanApp.kt?app/src/main/java/com/example/abonekaptanmobile/MainActivity.ktIapp/src/main/java/com/example/abonekaptanmobile/auth/GoogleAuthManager.ktIapp/src/main/java/com/example/abonekaptanmobile/data/local/AppDatabase.ktUapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/CommunityPatternDao.ktJapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/EmailDao.ktMapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/FeedbackDao.ktRapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/StageAnalysisDao.ktQapp/src/main/java/com/example/abonekaptanmobile/data/local/dao/SubscriptionDao.ktPapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/EmailEntity.ktSapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/FeedbackEntity.ktWapp/src/main/java/com/example/abonekaptanmobile/data/local/entity/SubscriptionEntity.kt^app/src/main/java/com/example/abonekaptanmobile/data/local/entity/SubscriptionPatternEntity.ktFapp/src/main/java/com/example/abonekaptanmobile/data/remote/GroqApi.ktMapp/src/main/java/com/example/abonekaptanmobile/data/remote/HuggingFaceApi.ktMapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GmailApi.ktQapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GmailMessage.ktOapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/GroqModels.ktVapp/src/main/java/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.kt]app/src/main/java/com/example/abonekaptanmobile/data/repository/CommunityPatternRepository.ktRapp/src/main/java/com/example/abonekaptanmobile/data/repository/EmailRepository.ktUapp/src/main/java/com/example/abonekaptanmobile/data/repository/FeedbackRepository.ktRapp/src/main/java/com/example/abonekaptanmobile/data/repository/GmailRepository.ktQapp/src/main/java/com/example/abonekaptanmobile/data/repository/GroqRepository.ktXapp/src/main/java/com/example/abonekaptanmobile/data/repository/HuggingFaceRepository.ktZapp/src/main/java/com/example/abonekaptanmobile/data/repository/StageAnalysisRepository.ktYapp/src/main/java/com/example/abonekaptanmobile/data/repository/SubscriptionRepository.kt?app/src/main/java/com/example/abonekaptanmobile/di/AppModule.ktIapp/src/main/java/com/example/abonekaptanmobile/model/CancellationInfo.ktHapp/src/main/java/com/example/abonekaptanmobile/model/ClassifiedEmail.ktAapp/src/main/java/com/example/abonekaptanmobile/model/RawEmail.ktIapp/src/main/java/com/example/abonekaptanmobile/model/SubscriptionItem.ktRapp/src/main/java/com/example/abonekaptanmobile/services/SubscriptionClassifier.ktLapp/src/main/java/com/example/abonekaptanmobile/ui/screens/FeedbackDialog.ktKapp/src/main/java/com/example/abonekaptanmobile/ui/screens/LabTestDialog.ktJapp/src/main/java/com/example/abonekaptanmobile/ui/screens/SignInScreen.ktQapp/src/main/java/com/example/abonekaptanmobile/ui/screens/StageAnalysisScreen.ktTapp/src/main/java/com/example/abonekaptanmobile/ui/screens/SubscriptionListScreen.ktAapp/src/main/java/com/example/abonekaptanmobile/ui/theme/Color.ktAapp/src/main/java/com/example/abonekaptanmobile/ui/theme/Theme.kt@app/src/main/java/com/example/abonekaptanmobile/ui/theme/Type.ktMapp/src/main/java/com/example/abonekaptanmobile/ui/viewmodel/MainViewModel.ktVapp/src/main/java/com/example/abonekaptanmobile/ui/viewmodel/StageAnalysisViewModel.ktMapp/src/main/java/com/example/abonekaptanmobile/utils/HybridApproachTester.ktPapp/src/main/java/com/example/abonekaptanmobile/workers/ProcessFeedbackWorker.kt                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              