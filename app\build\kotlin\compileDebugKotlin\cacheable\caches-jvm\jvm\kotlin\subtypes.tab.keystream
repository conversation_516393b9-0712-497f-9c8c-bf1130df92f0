kotlin.Enumandroidx.lifecycle.ViewModelandroid.app.Application$androidx.work.Configuration.Provider#androidx.activity.ComponentActivityandroidx.room.RoomDatabaseandroidx.work.CoroutineWorker4com.example.abonekaptanmobile.data.local.AppDatabaseDcom.example.abonekaptanmobile.data.local.dao.Stage1AnalysisResultDaodagger.internal.Factory<com.example.abonekaptanmobile.data.local.dao.SubscriptionDao=<EMAIL>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                