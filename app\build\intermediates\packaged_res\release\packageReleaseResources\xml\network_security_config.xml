<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">api-inference.huggingface.co</domain>
        <trust-anchors>
            <!-- Trust system certificates -->
            <certificates src="system"/>
            <!-- Trust user added certificates -->
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>
    
    <!-- Allow cleartext traffic for debugging -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </base-config>
</network-security-config>
