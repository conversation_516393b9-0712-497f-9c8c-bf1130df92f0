// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.ui.viewmodel;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class Stage1AnalysisViewModel_Factory implements Factory<Stage1AnalysisViewModel> {
  @Override
  public Stage1AnalysisViewModel get() {
    return newInstance();
  }

  public static Stage1AnalysisViewModel_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static Stage1AnalysisViewModel newInstance() {
    return new Stage1AnalysisViewModel();
  }

  private static final class InstanceHolder {
    private static final Stage1AnalysisViewModel_Factory INSTANCE = new Stage1AnalysisViewModel_Factory();
  }
}
