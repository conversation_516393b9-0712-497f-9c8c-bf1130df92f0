package com.example.abonekaptanmobile.data.repository;

import android.util.Base64;
import android.util.Log;
import com.example.abonekaptanmobile.data.remote.GmailApi;
import com.example.abonekaptanmobile.data.remote.model.GmailMessage;
import com.example.abonekaptanmobile.data.remote.model.MessagePayload;
import com.example.abonekaptanmobile.model.RawEmail;
import kotlinx.coroutines.Dispatchers;
import java.nio.charset.StandardCharsets;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\n\u001a\u00020\u00072\u0006\u0010\u000b\u001a\u00020\u0007H\u0002J\u0012\u0010\f\u001a\u0004\u0018\u00010\u00072\u0006\u0010\r\u001a\u00020\u0007H\u0002J \u0010\u000e\u001a\u0010\u0012\u0004\u0012\u00020\u0007\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0011H\u0002J(\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u00062\b\b\u0002\u0010\u0014\u001a\u00020\u00152\b\b\u0002\u0010\u0016\u001a\u00020\u0015H\u0086@\u00a2\u0006\u0002\u0010\u0017J\u0010\u0010\u0018\u001a\u00020\u00192\u0006\u0010\u001a\u001a\u00020\u0013H\u0002J\u0012\u0010\u001b\u001a\u0004\u0018\u00010\u00132\u0006\u0010\u001c\u001a\u00020\u001dH\u0002R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001e"}, d2 = {"Lcom/example/abonekaptanmobile/data/repository/GmailRepository;", "", "gmailApi", "Lcom/example/abonekaptanmobile/data/remote/GmailApi;", "(Lcom/example/abonekaptanmobile/data/remote/GmailApi;)V", "negativeKeywords", "", "", "positiveKeywords", "trustedSenderDomains", "decodeBase64", "encodedString", "extractDomain", "emailAddress", "extractTextParts", "Lkotlin/Pair;", "payload", "Lcom/example/abonekaptanmobile/data/remote/model/MessagePayload;", "fetchEmails", "Lcom/example/abonekaptanmobile/model/RawEmail;", "maxResultsPerQuery", "", "maxTotalEmails", "(IILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "isPotentiallySubscriptionRelated", "", "email", "parseGmailMessage", "message", "Lcom/example/abonekaptanmobile/data/remote/model/GmailMessage;", "app_release"})
public final class GmailRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.remote.GmailApi gmailApi = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> trustedSenderDomains = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> positiveKeywords = null;
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<java.lang.String> negativeKeywords = null;
    
    @javax.inject.Inject()
    public GmailRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.GmailApi gmailApi) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object fetchEmails(int maxResultsPerQuery, int maxTotalEmails, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.model.RawEmail>> $completion) {
        return null;
    }
    
    private final com.example.abonekaptanmobile.model.RawEmail parseGmailMessage(com.example.abonekaptanmobile.data.remote.model.GmailMessage message) {
        return null;
    }
    
    private final boolean isPotentiallySubscriptionRelated(com.example.abonekaptanmobile.model.RawEmail email) {
        return false;
    }
    
    private final java.lang.String extractDomain(java.lang.String emailAddress) {
        return null;
    }
    
    private final kotlin.Pair<java.lang.String, java.lang.String> extractTextParts(com.example.abonekaptanmobile.data.remote.model.MessagePayload payload) {
        return null;
    }
    
    private final java.lang.String decodeBase64(java.lang.String encodedString) {
        return null;
    }
}