package com.example.abonekaptanmobile.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class Stage1AnalysisResultDao_Impl implements Stage1AnalysisResultDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Stage1AnalysisResult> __insertionAdapterOfStage1AnalysisResult;

  private final EntityDeletionOrUpdateAdapter<Stage1AnalysisResult> __deletionAdapterOfStage1AnalysisResult;

  private final EntityDeletionOrUpdateAdapter<Stage1AnalysisResult> __updateAdapterOfStage1AnalysisResult;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllResults;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldResults;

  public Stage1AnalysisResultDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfStage1AnalysisResult = new EntityInsertionAdapter<Stage1AnalysisResult>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `stage1_analysis_results` (`id`,`emailIndex`,`senderName`,`domain`,`subject`,`aiResponse`,`isSubscriptionCompany`,`companyName`,`companyConfidence`,`analysisTimestamp`,`batchNumber`,`totalBatches`,`groqModel`,`promptUsed`,`rawEmailSnippet`,`processingNotes`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Stage1AnalysisResult entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getEmailIndex());
        if (entity.getSenderName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSenderName());
        }
        if (entity.getDomain() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDomain());
        }
        if (entity.getSubject() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSubject());
        }
        if (entity.getAiResponse() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAiResponse());
        }
        final int _tmp = entity.isSubscriptionCompany() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getCompanyName() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getCompanyName());
        }
        statement.bindDouble(9, entity.getCompanyConfidence());
        statement.bindLong(10, entity.getAnalysisTimestamp());
        statement.bindLong(11, entity.getBatchNumber());
        statement.bindLong(12, entity.getTotalBatches());
        if (entity.getGroqModel() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getGroqModel());
        }
        if (entity.getPromptUsed() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getPromptUsed());
        }
        if (entity.getRawEmailSnippet() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getRawEmailSnippet());
        }
        if (entity.getProcessingNotes() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getProcessingNotes());
        }
      }
    };
    this.__deletionAdapterOfStage1AnalysisResult = new EntityDeletionOrUpdateAdapter<Stage1AnalysisResult>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `stage1_analysis_results` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Stage1AnalysisResult entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfStage1AnalysisResult = new EntityDeletionOrUpdateAdapter<Stage1AnalysisResult>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `stage1_analysis_results` SET `id` = ?,`emailIndex` = ?,`senderName` = ?,`domain` = ?,`subject` = ?,`aiResponse` = ?,`isSubscriptionCompany` = ?,`companyName` = ?,`companyConfidence` = ?,`analysisTimestamp` = ?,`batchNumber` = ?,`totalBatches` = ?,`groqModel` = ?,`promptUsed` = ?,`rawEmailSnippet` = ?,`processingNotes` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Stage1AnalysisResult entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getEmailIndex());
        if (entity.getSenderName() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getSenderName());
        }
        if (entity.getDomain() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getDomain());
        }
        if (entity.getSubject() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSubject());
        }
        if (entity.getAiResponse() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getAiResponse());
        }
        final int _tmp = entity.isSubscriptionCompany() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getCompanyName() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getCompanyName());
        }
        statement.bindDouble(9, entity.getCompanyConfidence());
        statement.bindLong(10, entity.getAnalysisTimestamp());
        statement.bindLong(11, entity.getBatchNumber());
        statement.bindLong(12, entity.getTotalBatches());
        if (entity.getGroqModel() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getGroqModel());
        }
        if (entity.getPromptUsed() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getPromptUsed());
        }
        if (entity.getRawEmailSnippet() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getRawEmailSnippet());
        }
        if (entity.getProcessingNotes() == null) {
          statement.bindNull(16);
        } else {
          statement.bindString(16, entity.getProcessingNotes());
        }
        statement.bindLong(17, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAllResults = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM stage1_analysis_results";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldResults = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM stage1_analysis_results WHERE analysisTimestamp < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertResult(final Stage1AnalysisResult result,
      final Continuation<? super Long> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Long>() {
      @Override
      @NonNull
      public Long call() throws Exception {
        __db.beginTransaction();
        try {
          final Long _result = __insertionAdapterOfStage1AnalysisResult.insertAndReturnId(result);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertResults(final List<Stage1AnalysisResult> results,
      final Continuation<? super List<Long>> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<List<Long>>() {
      @Override
      @NonNull
      public List<Long> call() throws Exception {
        __db.beginTransaction();
        try {
          final List<Long> _result = __insertionAdapterOfStage1AnalysisResult.insertAndReturnIdsList(results);
          __db.setTransactionSuccessful();
          return _result;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteResult(final Stage1AnalysisResult result,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfStage1AnalysisResult.handle(result);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateResult(final Stage1AnalysisResult result,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfStage1AnalysisResult.handle(result);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllResults(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllResults.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllResults.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldResults(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldResults.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldResults.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<Stage1AnalysisResult>> getAllResults() {
    final String _sql = "SELECT * FROM stage1_analysis_results ORDER BY analysisTimestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"stage1_analysis_results"}, new Callable<List<Stage1AnalysisResult>>() {
      @Override
      @NonNull
      public List<Stage1AnalysisResult> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfSenderName = CursorUtil.getColumnIndexOrThrow(_cursor, "senderName");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfIsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscriptionCompany");
          final int _cursorIndexOfCompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "companyName");
          final int _cursorIndexOfCompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "companyConfidence");
          final int _cursorIndexOfAnalysisTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "analysisTimestamp");
          final int _cursorIndexOfBatchNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "batchNumber");
          final int _cursorIndexOfTotalBatches = CursorUtil.getColumnIndexOrThrow(_cursor, "totalBatches");
          final int _cursorIndexOfGroqModel = CursorUtil.getColumnIndexOrThrow(_cursor, "groqModel");
          final int _cursorIndexOfPromptUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "promptUsed");
          final int _cursorIndexOfRawEmailSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "rawEmailSnippet");
          final int _cursorIndexOfProcessingNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "processingNotes");
          final List<Stage1AnalysisResult> _result = new ArrayList<Stage1AnalysisResult>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Stage1AnalysisResult _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpSenderName;
            if (_cursor.isNull(_cursorIndexOfSenderName)) {
              _tmpSenderName = null;
            } else {
              _tmpSenderName = _cursor.getString(_cursorIndexOfSenderName);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final boolean _tmpIsSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscriptionCompany);
            _tmpIsSubscriptionCompany = _tmp != 0;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final float _tmpCompanyConfidence;
            _tmpCompanyConfidence = _cursor.getFloat(_cursorIndexOfCompanyConfidence);
            final long _tmpAnalysisTimestamp;
            _tmpAnalysisTimestamp = _cursor.getLong(_cursorIndexOfAnalysisTimestamp);
            final int _tmpBatchNumber;
            _tmpBatchNumber = _cursor.getInt(_cursorIndexOfBatchNumber);
            final int _tmpTotalBatches;
            _tmpTotalBatches = _cursor.getInt(_cursorIndexOfTotalBatches);
            final String _tmpGroqModel;
            if (_cursor.isNull(_cursorIndexOfGroqModel)) {
              _tmpGroqModel = null;
            } else {
              _tmpGroqModel = _cursor.getString(_cursorIndexOfGroqModel);
            }
            final String _tmpPromptUsed;
            if (_cursor.isNull(_cursorIndexOfPromptUsed)) {
              _tmpPromptUsed = null;
            } else {
              _tmpPromptUsed = _cursor.getString(_cursorIndexOfPromptUsed);
            }
            final String _tmpRawEmailSnippet;
            if (_cursor.isNull(_cursorIndexOfRawEmailSnippet)) {
              _tmpRawEmailSnippet = null;
            } else {
              _tmpRawEmailSnippet = _cursor.getString(_cursorIndexOfRawEmailSnippet);
            }
            final String _tmpProcessingNotes;
            if (_cursor.isNull(_cursorIndexOfProcessingNotes)) {
              _tmpProcessingNotes = null;
            } else {
              _tmpProcessingNotes = _cursor.getString(_cursorIndexOfProcessingNotes);
            }
            _item = new Stage1AnalysisResult(_tmpId,_tmpEmailIndex,_tmpSenderName,_tmpDomain,_tmpSubject,_tmpAiResponse,_tmpIsSubscriptionCompany,_tmpCompanyName,_tmpCompanyConfidence,_tmpAnalysisTimestamp,_tmpBatchNumber,_tmpTotalBatches,_tmpGroqModel,_tmpPromptUsed,_tmpRawEmailSnippet,_tmpProcessingNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getResultsByBatch(final int batchNumber,
      final Continuation<? super List<Stage1AnalysisResult>> $completion) {
    final String _sql = "SELECT * FROM stage1_analysis_results WHERE batchNumber = ? ORDER BY emailIndex";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, batchNumber);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Stage1AnalysisResult>>() {
      @Override
      @NonNull
      public List<Stage1AnalysisResult> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfSenderName = CursorUtil.getColumnIndexOrThrow(_cursor, "senderName");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfIsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscriptionCompany");
          final int _cursorIndexOfCompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "companyName");
          final int _cursorIndexOfCompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "companyConfidence");
          final int _cursorIndexOfAnalysisTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "analysisTimestamp");
          final int _cursorIndexOfBatchNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "batchNumber");
          final int _cursorIndexOfTotalBatches = CursorUtil.getColumnIndexOrThrow(_cursor, "totalBatches");
          final int _cursorIndexOfGroqModel = CursorUtil.getColumnIndexOrThrow(_cursor, "groqModel");
          final int _cursorIndexOfPromptUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "promptUsed");
          final int _cursorIndexOfRawEmailSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "rawEmailSnippet");
          final int _cursorIndexOfProcessingNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "processingNotes");
          final List<Stage1AnalysisResult> _result = new ArrayList<Stage1AnalysisResult>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Stage1AnalysisResult _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpSenderName;
            if (_cursor.isNull(_cursorIndexOfSenderName)) {
              _tmpSenderName = null;
            } else {
              _tmpSenderName = _cursor.getString(_cursorIndexOfSenderName);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final boolean _tmpIsSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscriptionCompany);
            _tmpIsSubscriptionCompany = _tmp != 0;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final float _tmpCompanyConfidence;
            _tmpCompanyConfidence = _cursor.getFloat(_cursorIndexOfCompanyConfidence);
            final long _tmpAnalysisTimestamp;
            _tmpAnalysisTimestamp = _cursor.getLong(_cursorIndexOfAnalysisTimestamp);
            final int _tmpBatchNumber;
            _tmpBatchNumber = _cursor.getInt(_cursorIndexOfBatchNumber);
            final int _tmpTotalBatches;
            _tmpTotalBatches = _cursor.getInt(_cursorIndexOfTotalBatches);
            final String _tmpGroqModel;
            if (_cursor.isNull(_cursorIndexOfGroqModel)) {
              _tmpGroqModel = null;
            } else {
              _tmpGroqModel = _cursor.getString(_cursorIndexOfGroqModel);
            }
            final String _tmpPromptUsed;
            if (_cursor.isNull(_cursorIndexOfPromptUsed)) {
              _tmpPromptUsed = null;
            } else {
              _tmpPromptUsed = _cursor.getString(_cursorIndexOfPromptUsed);
            }
            final String _tmpRawEmailSnippet;
            if (_cursor.isNull(_cursorIndexOfRawEmailSnippet)) {
              _tmpRawEmailSnippet = null;
            } else {
              _tmpRawEmailSnippet = _cursor.getString(_cursorIndexOfRawEmailSnippet);
            }
            final String _tmpProcessingNotes;
            if (_cursor.isNull(_cursorIndexOfProcessingNotes)) {
              _tmpProcessingNotes = null;
            } else {
              _tmpProcessingNotes = _cursor.getString(_cursorIndexOfProcessingNotes);
            }
            _item = new Stage1AnalysisResult(_tmpId,_tmpEmailIndex,_tmpSenderName,_tmpDomain,_tmpSubject,_tmpAiResponse,_tmpIsSubscriptionCompany,_tmpCompanyName,_tmpCompanyConfidence,_tmpAnalysisTimestamp,_tmpBatchNumber,_tmpTotalBatches,_tmpGroqModel,_tmpPromptUsed,_tmpRawEmailSnippet,_tmpProcessingNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getYesResponses(
      final Continuation<? super List<Stage1AnalysisResult>> $completion) {
    final String _sql = "SELECT * FROM stage1_analysis_results WHERE aiResponse = 'EVET' ORDER BY analysisTimestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Stage1AnalysisResult>>() {
      @Override
      @NonNull
      public List<Stage1AnalysisResult> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfSenderName = CursorUtil.getColumnIndexOrThrow(_cursor, "senderName");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfIsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscriptionCompany");
          final int _cursorIndexOfCompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "companyName");
          final int _cursorIndexOfCompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "companyConfidence");
          final int _cursorIndexOfAnalysisTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "analysisTimestamp");
          final int _cursorIndexOfBatchNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "batchNumber");
          final int _cursorIndexOfTotalBatches = CursorUtil.getColumnIndexOrThrow(_cursor, "totalBatches");
          final int _cursorIndexOfGroqModel = CursorUtil.getColumnIndexOrThrow(_cursor, "groqModel");
          final int _cursorIndexOfPromptUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "promptUsed");
          final int _cursorIndexOfRawEmailSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "rawEmailSnippet");
          final int _cursorIndexOfProcessingNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "processingNotes");
          final List<Stage1AnalysisResult> _result = new ArrayList<Stage1AnalysisResult>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Stage1AnalysisResult _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpSenderName;
            if (_cursor.isNull(_cursorIndexOfSenderName)) {
              _tmpSenderName = null;
            } else {
              _tmpSenderName = _cursor.getString(_cursorIndexOfSenderName);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final boolean _tmpIsSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscriptionCompany);
            _tmpIsSubscriptionCompany = _tmp != 0;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final float _tmpCompanyConfidence;
            _tmpCompanyConfidence = _cursor.getFloat(_cursorIndexOfCompanyConfidence);
            final long _tmpAnalysisTimestamp;
            _tmpAnalysisTimestamp = _cursor.getLong(_cursorIndexOfAnalysisTimestamp);
            final int _tmpBatchNumber;
            _tmpBatchNumber = _cursor.getInt(_cursorIndexOfBatchNumber);
            final int _tmpTotalBatches;
            _tmpTotalBatches = _cursor.getInt(_cursorIndexOfTotalBatches);
            final String _tmpGroqModel;
            if (_cursor.isNull(_cursorIndexOfGroqModel)) {
              _tmpGroqModel = null;
            } else {
              _tmpGroqModel = _cursor.getString(_cursorIndexOfGroqModel);
            }
            final String _tmpPromptUsed;
            if (_cursor.isNull(_cursorIndexOfPromptUsed)) {
              _tmpPromptUsed = null;
            } else {
              _tmpPromptUsed = _cursor.getString(_cursorIndexOfPromptUsed);
            }
            final String _tmpRawEmailSnippet;
            if (_cursor.isNull(_cursorIndexOfRawEmailSnippet)) {
              _tmpRawEmailSnippet = null;
            } else {
              _tmpRawEmailSnippet = _cursor.getString(_cursorIndexOfRawEmailSnippet);
            }
            final String _tmpProcessingNotes;
            if (_cursor.isNull(_cursorIndexOfProcessingNotes)) {
              _tmpProcessingNotes = null;
            } else {
              _tmpProcessingNotes = _cursor.getString(_cursorIndexOfProcessingNotes);
            }
            _item = new Stage1AnalysisResult(_tmpId,_tmpEmailIndex,_tmpSenderName,_tmpDomain,_tmpSubject,_tmpAiResponse,_tmpIsSubscriptionCompany,_tmpCompanyName,_tmpCompanyConfidence,_tmpAnalysisTimestamp,_tmpBatchNumber,_tmpTotalBatches,_tmpGroqModel,_tmpPromptUsed,_tmpRawEmailSnippet,_tmpProcessingNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getNoResponses(final Continuation<? super List<Stage1AnalysisResult>> $completion) {
    final String _sql = "SELECT * FROM stage1_analysis_results WHERE aiResponse = 'HAYIR' ORDER BY analysisTimestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Stage1AnalysisResult>>() {
      @Override
      @NonNull
      public List<Stage1AnalysisResult> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfSenderName = CursorUtil.getColumnIndexOrThrow(_cursor, "senderName");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfIsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscriptionCompany");
          final int _cursorIndexOfCompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "companyName");
          final int _cursorIndexOfCompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "companyConfidence");
          final int _cursorIndexOfAnalysisTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "analysisTimestamp");
          final int _cursorIndexOfBatchNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "batchNumber");
          final int _cursorIndexOfTotalBatches = CursorUtil.getColumnIndexOrThrow(_cursor, "totalBatches");
          final int _cursorIndexOfGroqModel = CursorUtil.getColumnIndexOrThrow(_cursor, "groqModel");
          final int _cursorIndexOfPromptUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "promptUsed");
          final int _cursorIndexOfRawEmailSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "rawEmailSnippet");
          final int _cursorIndexOfProcessingNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "processingNotes");
          final List<Stage1AnalysisResult> _result = new ArrayList<Stage1AnalysisResult>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Stage1AnalysisResult _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpSenderName;
            if (_cursor.isNull(_cursorIndexOfSenderName)) {
              _tmpSenderName = null;
            } else {
              _tmpSenderName = _cursor.getString(_cursorIndexOfSenderName);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final boolean _tmpIsSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscriptionCompany);
            _tmpIsSubscriptionCompany = _tmp != 0;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final float _tmpCompanyConfidence;
            _tmpCompanyConfidence = _cursor.getFloat(_cursorIndexOfCompanyConfidence);
            final long _tmpAnalysisTimestamp;
            _tmpAnalysisTimestamp = _cursor.getLong(_cursorIndexOfAnalysisTimestamp);
            final int _tmpBatchNumber;
            _tmpBatchNumber = _cursor.getInt(_cursorIndexOfBatchNumber);
            final int _tmpTotalBatches;
            _tmpTotalBatches = _cursor.getInt(_cursorIndexOfTotalBatches);
            final String _tmpGroqModel;
            if (_cursor.isNull(_cursorIndexOfGroqModel)) {
              _tmpGroqModel = null;
            } else {
              _tmpGroqModel = _cursor.getString(_cursorIndexOfGroqModel);
            }
            final String _tmpPromptUsed;
            if (_cursor.isNull(_cursorIndexOfPromptUsed)) {
              _tmpPromptUsed = null;
            } else {
              _tmpPromptUsed = _cursor.getString(_cursorIndexOfPromptUsed);
            }
            final String _tmpRawEmailSnippet;
            if (_cursor.isNull(_cursorIndexOfRawEmailSnippet)) {
              _tmpRawEmailSnippet = null;
            } else {
              _tmpRawEmailSnippet = _cursor.getString(_cursorIndexOfRawEmailSnippet);
            }
            final String _tmpProcessingNotes;
            if (_cursor.isNull(_cursorIndexOfProcessingNotes)) {
              _tmpProcessingNotes = null;
            } else {
              _tmpProcessingNotes = _cursor.getString(_cursorIndexOfProcessingNotes);
            }
            _item = new Stage1AnalysisResult(_tmpId,_tmpEmailIndex,_tmpSenderName,_tmpDomain,_tmpSubject,_tmpAiResponse,_tmpIsSubscriptionCompany,_tmpCompanyName,_tmpCompanyConfidence,_tmpAnalysisTimestamp,_tmpBatchNumber,_tmpTotalBatches,_tmpGroqModel,_tmpPromptUsed,_tmpRawEmailSnippet,_tmpProcessingNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getResultsByDomain(final String domain,
      final Continuation<? super List<Stage1AnalysisResult>> $completion) {
    final String _sql = "SELECT * FROM stage1_analysis_results WHERE domain LIKE '%' || ? || '%' ORDER BY analysisTimestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (domain == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, domain);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Stage1AnalysisResult>>() {
      @Override
      @NonNull
      public List<Stage1AnalysisResult> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfSenderName = CursorUtil.getColumnIndexOrThrow(_cursor, "senderName");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfIsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscriptionCompany");
          final int _cursorIndexOfCompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "companyName");
          final int _cursorIndexOfCompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "companyConfidence");
          final int _cursorIndexOfAnalysisTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "analysisTimestamp");
          final int _cursorIndexOfBatchNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "batchNumber");
          final int _cursorIndexOfTotalBatches = CursorUtil.getColumnIndexOrThrow(_cursor, "totalBatches");
          final int _cursorIndexOfGroqModel = CursorUtil.getColumnIndexOrThrow(_cursor, "groqModel");
          final int _cursorIndexOfPromptUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "promptUsed");
          final int _cursorIndexOfRawEmailSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "rawEmailSnippet");
          final int _cursorIndexOfProcessingNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "processingNotes");
          final List<Stage1AnalysisResult> _result = new ArrayList<Stage1AnalysisResult>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Stage1AnalysisResult _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpSenderName;
            if (_cursor.isNull(_cursorIndexOfSenderName)) {
              _tmpSenderName = null;
            } else {
              _tmpSenderName = _cursor.getString(_cursorIndexOfSenderName);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final boolean _tmpIsSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscriptionCompany);
            _tmpIsSubscriptionCompany = _tmp != 0;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final float _tmpCompanyConfidence;
            _tmpCompanyConfidence = _cursor.getFloat(_cursorIndexOfCompanyConfidence);
            final long _tmpAnalysisTimestamp;
            _tmpAnalysisTimestamp = _cursor.getLong(_cursorIndexOfAnalysisTimestamp);
            final int _tmpBatchNumber;
            _tmpBatchNumber = _cursor.getInt(_cursorIndexOfBatchNumber);
            final int _tmpTotalBatches;
            _tmpTotalBatches = _cursor.getInt(_cursorIndexOfTotalBatches);
            final String _tmpGroqModel;
            if (_cursor.isNull(_cursorIndexOfGroqModel)) {
              _tmpGroqModel = null;
            } else {
              _tmpGroqModel = _cursor.getString(_cursorIndexOfGroqModel);
            }
            final String _tmpPromptUsed;
            if (_cursor.isNull(_cursorIndexOfPromptUsed)) {
              _tmpPromptUsed = null;
            } else {
              _tmpPromptUsed = _cursor.getString(_cursorIndexOfPromptUsed);
            }
            final String _tmpRawEmailSnippet;
            if (_cursor.isNull(_cursorIndexOfRawEmailSnippet)) {
              _tmpRawEmailSnippet = null;
            } else {
              _tmpRawEmailSnippet = _cursor.getString(_cursorIndexOfRawEmailSnippet);
            }
            final String _tmpProcessingNotes;
            if (_cursor.isNull(_cursorIndexOfProcessingNotes)) {
              _tmpProcessingNotes = null;
            } else {
              _tmpProcessingNotes = _cursor.getString(_cursorIndexOfProcessingNotes);
            }
            _item = new Stage1AnalysisResult(_tmpId,_tmpEmailIndex,_tmpSenderName,_tmpDomain,_tmpSubject,_tmpAiResponse,_tmpIsSubscriptionCompany,_tmpCompanyName,_tmpCompanyConfidence,_tmpAnalysisTimestamp,_tmpBatchNumber,_tmpTotalBatches,_tmpGroqModel,_tmpPromptUsed,_tmpRawEmailSnippet,_tmpProcessingNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getResultsBySenderName(final String senderName,
      final Continuation<? super List<Stage1AnalysisResult>> $completion) {
    final String _sql = "SELECT * FROM stage1_analysis_results WHERE senderName LIKE '%' || ? || '%' ORDER BY analysisTimestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (senderName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, senderName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Stage1AnalysisResult>>() {
      @Override
      @NonNull
      public List<Stage1AnalysisResult> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfEmailIndex = CursorUtil.getColumnIndexOrThrow(_cursor, "emailIndex");
          final int _cursorIndexOfSenderName = CursorUtil.getColumnIndexOrThrow(_cursor, "senderName");
          final int _cursorIndexOfDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "domain");
          final int _cursorIndexOfSubject = CursorUtil.getColumnIndexOrThrow(_cursor, "subject");
          final int _cursorIndexOfAiResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "aiResponse");
          final int _cursorIndexOfIsSubscriptionCompany = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscriptionCompany");
          final int _cursorIndexOfCompanyName = CursorUtil.getColumnIndexOrThrow(_cursor, "companyName");
          final int _cursorIndexOfCompanyConfidence = CursorUtil.getColumnIndexOrThrow(_cursor, "companyConfidence");
          final int _cursorIndexOfAnalysisTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "analysisTimestamp");
          final int _cursorIndexOfBatchNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "batchNumber");
          final int _cursorIndexOfTotalBatches = CursorUtil.getColumnIndexOrThrow(_cursor, "totalBatches");
          final int _cursorIndexOfGroqModel = CursorUtil.getColumnIndexOrThrow(_cursor, "groqModel");
          final int _cursorIndexOfPromptUsed = CursorUtil.getColumnIndexOrThrow(_cursor, "promptUsed");
          final int _cursorIndexOfRawEmailSnippet = CursorUtil.getColumnIndexOrThrow(_cursor, "rawEmailSnippet");
          final int _cursorIndexOfProcessingNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "processingNotes");
          final List<Stage1AnalysisResult> _result = new ArrayList<Stage1AnalysisResult>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Stage1AnalysisResult _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final int _tmpEmailIndex;
            _tmpEmailIndex = _cursor.getInt(_cursorIndexOfEmailIndex);
            final String _tmpSenderName;
            if (_cursor.isNull(_cursorIndexOfSenderName)) {
              _tmpSenderName = null;
            } else {
              _tmpSenderName = _cursor.getString(_cursorIndexOfSenderName);
            }
            final String _tmpDomain;
            if (_cursor.isNull(_cursorIndexOfDomain)) {
              _tmpDomain = null;
            } else {
              _tmpDomain = _cursor.getString(_cursorIndexOfDomain);
            }
            final String _tmpSubject;
            if (_cursor.isNull(_cursorIndexOfSubject)) {
              _tmpSubject = null;
            } else {
              _tmpSubject = _cursor.getString(_cursorIndexOfSubject);
            }
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final boolean _tmpIsSubscriptionCompany;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscriptionCompany);
            _tmpIsSubscriptionCompany = _tmp != 0;
            final String _tmpCompanyName;
            if (_cursor.isNull(_cursorIndexOfCompanyName)) {
              _tmpCompanyName = null;
            } else {
              _tmpCompanyName = _cursor.getString(_cursorIndexOfCompanyName);
            }
            final float _tmpCompanyConfidence;
            _tmpCompanyConfidence = _cursor.getFloat(_cursorIndexOfCompanyConfidence);
            final long _tmpAnalysisTimestamp;
            _tmpAnalysisTimestamp = _cursor.getLong(_cursorIndexOfAnalysisTimestamp);
            final int _tmpBatchNumber;
            _tmpBatchNumber = _cursor.getInt(_cursorIndexOfBatchNumber);
            final int _tmpTotalBatches;
            _tmpTotalBatches = _cursor.getInt(_cursorIndexOfTotalBatches);
            final String _tmpGroqModel;
            if (_cursor.isNull(_cursorIndexOfGroqModel)) {
              _tmpGroqModel = null;
            } else {
              _tmpGroqModel = _cursor.getString(_cursorIndexOfGroqModel);
            }
            final String _tmpPromptUsed;
            if (_cursor.isNull(_cursorIndexOfPromptUsed)) {
              _tmpPromptUsed = null;
            } else {
              _tmpPromptUsed = _cursor.getString(_cursorIndexOfPromptUsed);
            }
            final String _tmpRawEmailSnippet;
            if (_cursor.isNull(_cursorIndexOfRawEmailSnippet)) {
              _tmpRawEmailSnippet = null;
            } else {
              _tmpRawEmailSnippet = _cursor.getString(_cursorIndexOfRawEmailSnippet);
            }
            final String _tmpProcessingNotes;
            if (_cursor.isNull(_cursorIndexOfProcessingNotes)) {
              _tmpProcessingNotes = null;
            } else {
              _tmpProcessingNotes = _cursor.getString(_cursorIndexOfProcessingNotes);
            }
            _item = new Stage1AnalysisResult(_tmpId,_tmpEmailIndex,_tmpSenderName,_tmpDomain,_tmpSubject,_tmpAiResponse,_tmpIsSubscriptionCompany,_tmpCompanyName,_tmpCompanyConfidence,_tmpAnalysisTimestamp,_tmpBatchNumber,_tmpTotalBatches,_tmpGroqModel,_tmpPromptUsed,_tmpRawEmailSnippet,_tmpProcessingNotes);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getLatestAnalysisTimestamp(final Continuation<? super Long> $completion) {
    final String _sql = "SELECT MAX(analysisTimestamp) FROM stage1_analysis_results";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Long>() {
      @Override
      @Nullable
      public Long call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Long _result;
          if (_cursor.moveToFirst()) {
            final Long _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalAnalyzedCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM stage1_analysis_results";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getResponseStatistics(
      final Continuation<? super List<ResponseStatistic>> $completion) {
    final String _sql = "\n"
            + "        SELECT \n"
            + "            aiResponse,\n"
            + "            COUNT(*) as count\n"
            + "        FROM stage1_analysis_results \n"
            + "        GROUP BY aiResponse\n"
            + "    ";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<ResponseStatistic>>() {
      @Override
      @NonNull
      public List<ResponseStatistic> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfAiResponse = 0;
          final int _cursorIndexOfCount = 1;
          final List<ResponseStatistic> _result = new ArrayList<ResponseStatistic>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final ResponseStatistic _item;
            final String _tmpAiResponse;
            if (_cursor.isNull(_cursorIndexOfAiResponse)) {
              _tmpAiResponse = null;
            } else {
              _tmpAiResponse = _cursor.getString(_cursorIndexOfAiResponse);
            }
            final int _tmpCount;
            _tmpCount = _cursor.getInt(_cursorIndexOfCount);
            _item = new ResponseStatistic(_tmpAiResponse,_tmpCount);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
