package com.example.abonekaptanmobile.data.local;

import android.util.Log;
import androidx.room.Database;
import androidx.room.RoomDatabase;
import androidx.sqlite.db.SupportSQLiteDatabase;
import com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao;
import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;
import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao;
import com.example.abonekaptanmobile.data.local.dao.Stage1AnalysisResultDao;
import com.example.abonekaptanmobile.data.local.entity.FeedbackEntity;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity;
import com.example.abonekaptanmobile.data.local.entity.EmailEntity;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity;
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity;
import com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult;
import com.example.abonekaptanmobile.data.local.entity.PatternType;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u000f2\u00020\u0001:\u0001\u000fB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&J\b\u0010\u000b\u001a\u00020\fH&J\b\u0010\r\u001a\u00020\u000eH&\u00a8\u0006\u0010"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/AppDatabase;", "Landroidx/room/RoomDatabase;", "()V", "communityPatternDao", "Lcom/example/abonekaptanmobile/data/local/dao/CommunityPatternDao;", "emailDao", "Lcom/example/abonekaptanmobile/data/local/dao/EmailDao;", "feedbackDao", "Lcom/example/abonekaptanmobile/data/local/dao/FeedbackDao;", "stage1AnalysisResultDao", "Lcom/example/abonekaptanmobile/data/local/dao/Stage1AnalysisResultDao;", "stageAnalysisDao", "Lcom/example/abonekaptanmobile/data/local/dao/StageAnalysisDao;", "subscriptionDao", "Lcom/example/abonekaptanmobile/data/local/dao/SubscriptionDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity.class, com.example.abonekaptanmobile.data.local.entity.FeedbackEntity.class, com.example.abonekaptanmobile.data.local.entity.EmailEntity.class, com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity.class, com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity.class, com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult.class}, version = 6, exportSchema = true)
public abstract class AppDatabase extends androidx.room.RoomDatabase {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DATABASE_NAME = "abone_kaptan_db";
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_1_2 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_2_3 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_3_4 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_4_5 = null;
    @org.jetbrains.annotations.NotNull()
    private static final androidx.room.migration.Migration MIGRATION_5_6 = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.data.local.AppDatabase.Companion Companion = null;
    
    public AppDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.abonekaptanmobile.data.local.dao.CommunityPatternDao communityPatternDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.abonekaptanmobile.data.local.dao.FeedbackDao feedbackDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.abonekaptanmobile.data.local.dao.EmailDao emailDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.abonekaptanmobile.data.local.dao.SubscriptionDao subscriptionDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao stageAnalysisDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.abonekaptanmobile.data.local.dao.Stage1AnalysisResultDao stage1AnalysisResultDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u000b\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0011\u0010\t\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\bR\u0011\u0010\u000b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\bR\u0011\u0010\r\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\bR\u0011\u0010\u000f\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\b\u00a8\u0006\u0011"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/AppDatabase$Companion;", "", "()V", "DATABASE_NAME", "", "MIGRATION_1_2", "Landroidx/room/migration/Migration;", "getMIGRATION_1_2", "()Landroidx/room/migration/Migration;", "MIGRATION_2_3", "getMIGRATION_2_3", "MIGRATION_3_4", "getMIGRATION_3_4", "MIGRATION_4_5", "getMIGRATION_4_5", "MIGRATION_5_6", "getMIGRATION_5_6", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_1_2() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_2_3() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_3_4() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_4_5() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull()
        public final androidx.room.migration.Migration getMIGRATION_5_6() {
            return null;
        }
    }
}