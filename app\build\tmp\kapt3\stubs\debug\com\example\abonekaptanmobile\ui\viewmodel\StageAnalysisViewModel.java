package com.example.abonekaptanmobile.ui.viewmodel;

import androidx.lifecycle.ViewModel;
import com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics;
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity;
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import dagger.hilt.android.lifecycle.HiltViewModel;
import kotlinx.coroutines.flow.*;
import javax.inject.Inject;

/**
 * Turkish: İki aşamalı analiz sonuçları için ViewModel.
 * English: ViewModel for two-stage analysis results.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000l\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u000e\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0010$\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0006\u0010\u001d\u001a\u00020\u001eJ\u0006\u0010\u001f\u001a\u00020\u001eJ\u0010\u0010 \u001a\u00020\u001e2\b\b\u0002\u0010!\u001a\u00020\"J\u0018\u0010#\u001a\u0004\u0018\u00010\u000f2\u0006\u0010$\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010%J\u001a\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r2\u0006\u0010\'\u001a\u00020\u0007J\u0012\u0010(\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020*0\u000e0)J;\u0010+\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0)2\n\b\u0002\u0010\'\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010,\u001a\u0004\u0018\u00010\u00072\n\b\u0002\u0010-\u001a\u0004\u0018\u00010.\u00a2\u0006\u0002\u0010/J\u0018\u00100\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\"010)J\u0006\u00102\u001a\u00020\u001eJ\u0006\u00103\u001a\u00020\u001eJ\u0016\u00104\u001a\u00020\u001e2\u0006\u00105\u001a\u0002062\u0006\u00107\u001a\u00020\u0007J&\u00108\u001a\u00020\u001e2\u0006\u00105\u001a\u0002062\u0006\u00109\u001a\u00020\u00072\u0006\u0010:\u001a\u00020.2\u0006\u0010;\u001a\u00020\u0007R\u0016\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\b\u001a\b\u0012\u0004\u0012\u00020\t0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\n\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001d\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0011R\u0019\u0010\u0014\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0011R\u0017\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\t0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0011R\u001d\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0011R\u001d\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000f0\u000e0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010\u001b\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0011\u00a8\u0006<"}, d2 = {"Lcom/example/abonekaptanmobile/ui/viewmodel/StageAnalysisViewModel;", "Landroidx/lifecycle/ViewModel;", "stageAnalysisRepository", "Lcom/example/abonekaptanmobile/data/repository/StageAnalysisRepository;", "(Lcom/example/abonekaptanmobile/data/repository/StageAnalysisRepository;)V", "_error", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_isLoading", "", "_statistics", "Lcom/example/abonekaptanmobile/data/local/dao/AnalysisStatistics;", "analysisResults", "Lkotlinx/coroutines/flow/StateFlow;", "", "Lcom/example/abonekaptanmobile/data/local/entity/StageAnalysisEntity;", "getAnalysisResults", "()Lkotlinx/coroutines/flow/StateFlow;", "emailsForStage2", "getEmailsForStage2", "error", "getError", "isLoading", "stage1CompletedEmails", "getStage1CompletedEmails", "stage2CompletedEmails", "getStage2CompletedEmails", "statistics", "getStatistics", "clearError", "", "deleteAllAnalysisResults", "deleteOldAnalysisResults", "daysOld", "", "getAnalysisResultByEmailId", "emailId", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAnalysisResultsByCompany", "companyName", "getCompanyStatistics", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/abonekaptanmobile/data/local/dao/CompanyStatistics;", "getFilteredResults", "processingStatus", "minConfidence", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Float;)Lkotlinx/coroutines/flow/Flow;", "getSummaryInfo", "", "loadStatistics", "refreshData", "updateProcessingStatus", "entityId", "", "status", "updateStage2Result", "emailType", "confidence", "rawContent", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class StageAnalysisViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.StageAnalysisRepository stageAnalysisRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics> _statistics = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics> statistics = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> analysisResults = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> stage1CompletedEmails = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> emailsForStage2 = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> stage2CompletedEmails = null;
    
    @javax.inject.Inject()
    public StageAnalysisViewModel(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.StageAnalysisRepository stageAnalysisRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics> getStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getAnalysisResults() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getStage1CompletedEmails() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getEmailsForStage2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getStage2CompletedEmails() {
        return null;
    }
    
    /**
     * Turkish: İstatistikleri yükle.
     * English: Load statistics.
     */
    public final void loadStatistics() {
    }
    
    /**
     * Turkish: Belirli bir şirketin analiz sonuçlarını getir.
     * English: Get analysis results for a specific company.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getAnalysisResultsByCompany(@org.jetbrains.annotations.NotNull()
    java.lang.String companyName) {
        return null;
    }
    
    /**
     * Turkish: Tüm analiz sonuçlarını sil.
     * English: Delete all analysis results.
     */
    public final void deleteAllAnalysisResults() {
    }
    
    /**
     * Turkish: Eski analiz sonuçlarını sil.
     * English: Delete old analysis results.
     */
    public final void deleteOldAnalysisResults(int daysOld) {
    }
    
    /**
     * Turkish: Hatayı temizle.
     * English: Clear error.
     */
    public final void clearError() {
    }
    
    /**
     * Turkish: Şirket bazında istatistikleri getir.
     * English: Get company-based statistics.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.dao.CompanyStatistics>> getCompanyStatistics() {
        return null;
    }
    
    /**
     * Turkish: Analiz sonuçlarını yenile.
     * English: Refresh analysis results.
     */
    public final void refreshData() {
    }
    
    /**
     * Turkish: Belirli bir e-posta ID'sine göre analiz sonucunu getir.
     * English: Get analysis result by email ID.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getAnalysisResultByEmailId(@org.jetbrains.annotations.NotNull()
    java.lang.String emailId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> $completion) {
        return null;
    }
    
    /**
     * Turkish: Aşama 2 sonucunu güncelle.
     * English: Update stage 2 result.
     */
    public final void updateStage2Result(long entityId, @org.jetbrains.annotations.NotNull()
    java.lang.String emailType, float confidence, @org.jetbrains.annotations.NotNull()
    java.lang.String rawContent) {
    }
    
    /**
     * Turkish: İşlem durumunu güncelle.
     * English: Update processing status.
     */
    public final void updateProcessingStatus(long entityId, @org.jetbrains.annotations.NotNull()
    java.lang.String status) {
    }
    
    /**
     * Turkish: Filtrelenmiş sonuçları getir.
     * English: Get filtered results.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getFilteredResults(@org.jetbrains.annotations.Nullable()
    java.lang.String companyName, @org.jetbrains.annotations.Nullable()
    java.lang.String processingStatus, @org.jetbrains.annotations.Nullable()
    java.lang.Float minConfidence) {
        return null;
    }
    
    /**
     * Turkish: Özet bilgileri getir.
     * English: Get summary information.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.Map<java.lang.String, java.lang.Integer>> getSummaryInfo() {
        return null;
    }
}