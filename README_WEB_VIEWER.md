# Aşama 1 Email Analiz <PERSON>uçları Web Görüntüleyici

Bu web uygulaması, Abone Kaptan projesinin Aşama 1 email analiz sonuçlarını tablo halinde görüntülemek için oluşturulmuştur.

## 🎯 Özellikler

### 📊 Ana Özellikler
- **500 email analiz sonucunu** tablo halinde görüntüleme
- **EVET/HAYIR** sonuçlarını renkli gösterim
- **İstatistik kartları** (Toplam, EVET, HAYIR, Ortalama Güven)
- **Filtreleme** (Tümü, Sadece EVET, Sadece HAYIR)
- **Arama** (Domain, konu, şirket adı, gönderen)
- **CSV Export** özelliği
- **Responsive tasarım** (mobil uyumlu)

### 🎨 Görsel Özellikler
- Bootstrap 5 ile modern tasarım
- Font Awesome ikonları
- Renkli sonuç gösterimi (Yeşil: EVET, Kırmızı: HAYIR)
- Güven skoruna göre renk kodlaması
- Sticky header (kaydırılabilir tablo)

## 🚀 Kurulum ve Çalıştırma

### 1. Gereksinimler
```bash
pip install flask==2.3.3
```

### 2. Demo Veritabanı Oluşturma
```bash
python create_demo_db.py
```

### 3. Web Uygulamasını Başlatma
```bash
python web_viewer.py
```

### 4. Tarayıcıda Açma
```
http://localhost:5000
```

## 📁 Dosya Yapısı

```
├── web_viewer.py              # Ana Flask uygulaması
├── create_demo_db.py          # Demo veritabanı oluşturucu
├── check_db.py               # Veritabanı kontrol aracı
├── requirements.txt          # Python gereksinimleri
├── templates/
│   └── index.html           # Ana HTML template
├── abone_kaptan_db.sqlite   # SQLite veritabanı
└── README_WEB_VIEWER.md     # Bu dosya
```

## 🗄️ Veritabanı Yapısı

### stage_analysis Tablosu
```sql
CREATE TABLE stage_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    emailIndex INTEGER NOT NULL,
    emailId TEXT NOT NULL,
    domain TEXT NOT NULL,
    subject TEXT NOT NULL,
    `from` TEXT NOT NULL,
    date INTEGER NOT NULL,
    stage1_isSubscriptionCompany INTEGER NOT NULL,  -- 1: EVET, 0: HAYIR
    stage1_companyName TEXT NOT NULL,
    stage1_companyConfidence REAL NOT NULL,         -- 0.0 - 1.0 arası
    stage1_timestamp INTEGER NOT NULL,
    processingStatus TEXT NOT NULL,
    createdAt INTEGER NOT NULL
);
```

## 📊 Demo Veriler

Demo veritabanında **500 adet** email analiz sonucu bulunmaktadır:

### Abonelik Şirketleri (EVET)
- Netflix, Spotify, Amazon Prime, Disney+, YouTube Premium
- Adobe Creative Cloud, Microsoft 365, Dropbox, Zoom, Slack
- Canva, Figma, GitHub, Notion, Evernote, LastPass
- NordVPN, ExpressVPN, Coursera, Udemy, MasterClass
- The New York Times, Wall Street Journal, Medium, Substack

### Abonelik Olmayan Şirketler (HAYIR)
- Amazon, eBay, Alibaba, Booking.com, Airbnb, Uber, Lyft
- DoorDash, Grubhub, PayPal, Stripe, Square, Coinbase
- Binance, Tesla, Apple Store, Google Play, Steam, Epic Games
- Best Buy, Target, Walmart, Home Depot, IKEA, Zara

## 🔧 API Endpoints

### GET /
Ana sayfa - Tablo görünümü

### GET /api/stats
JSON formatında istatistikler
```json
{
    "total": 500,
    "yes_count": 274,
    "no_count": 226,
    "yes_percentage": 54.8,
    "no_percentage": 45.2,
    "avg_confidence": 0.58,
    "first_analysis": "2025-04-30 12:00:00",
    "last_analysis": "2025-05-30 02:00:00"
}
```

### GET /export/csv
CSV dosyası export (filtreleme parametreleri desteklenir)

## 🎛️ Filtreleme ve Arama

### Filtreler
- **Tümü**: Tüm sonuçları göster
- **Sadece EVET**: Sadece abonelik şirketi olanları göster
- **Sadece HAYIR**: Sadece abonelik şirketi olmayanları göster

### Arama
Aşağıdaki alanlarda arama yapabilirsiniz:
- Domain adı
- Email konusu
- Şirket adı
- Gönderen adresi

## 📈 İstatistikler

Web arayüzünde şu istatistikler gösterilir:
- **Toplam**: Analiz edilen email sayısı
- **EVET**: Abonelik şirketi olarak tespit edilenler (yüzde ile)
- **HAYIR**: Abonelik şirketi olmayan (yüzde ile)
- **Ortalama Güven**: Analiz güven skorunun ortalaması

## 🎨 Renk Kodlaması

### Sonuç Renkleri
- 🟢 **Yeşil**: EVET (Abonelik şirketi)
- 🔴 **Kırmızı**: HAYIR (Abonelik şirketi değil)

### Güven Skoru Renkleri
- 🟢 **Yeşil**: Yüksek güven (≥0.8)
- 🟠 **Turuncu**: Orta güven (0.5-0.8)
- 🔴 **Kırmızı**: Düşük güven (<0.5)

## 🔄 Otomatik Yenileme

Sayfa her 30 saniyede bir otomatik olarak yenilenir.

## 🛠️ Geliştirme Notları

- Flask development server kullanılmaktadır
- Debug mode aktiftir
- SQLite veritabanı kullanılmaktadır
- Bootstrap 5 ve Font Awesome CDN'den yüklenmektedir

## 📞 Destek

Herhangi bir sorun yaşarsanız:
1. Terminal çıktısını kontrol edin
2. Veritabanı dosyasının varlığını kontrol edin
3. Port 5000'in kullanımda olup olmadığını kontrol edin

---

**Not**: Bu demo uygulaması gerçek email verilerini değil, test amaçlı oluşturulmuş demo verileri kullanmaktadır.
