package com.example.abonekaptanmobile.data.repository

import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity
import com.example.abonekaptanmobile.model.SubscriptionItem
import com.example.abonekaptanmobile.model.SubscriptionStatus
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Turkish: Abonelik veritabanı işlemleri için repository.
 * English: Repository for subscription database operations.
 */
@Singleton
class SubscriptionRepository @Inject constructor(
    private val subscriptionDao: SubscriptionDao
) {

    /**
     * Turkish: Abonelikleri veritabanına kaydet.
     * English: Insert subscriptions into database.
     */
    suspend fun insertSubscriptions(subscriptions: List<SubscriptionItem>) {
        val subscriptionEntities = subscriptions.map { subscription ->
            SubscriptionEntity(
                serviceName = subscription.serviceName,
                status = subscription.status.name,
                emailCount = subscription.emailCount,
                lastEmailDate = subscription.lastEmailDate,
                cancellationDate = subscription.cancellationDate,
                subscriptionStartDate = subscription.subscriptionStartDate,
                relatedEmailIds = subscription.relatedEmailIds.joinToString(",")
            )
        }
        subscriptionDao.insertSubscriptions(subscriptionEntities)
    }

    /**
     * Turkish: Tüm abonelikleri getir.
     * English: Get all subscriptions.
     */
    fun getAllSubscriptions(): Flow<List<SubscriptionEntity>> {
        return subscriptionDao.getAllSubscriptions()
    }

    /**
     * Turkish: Aktif abonelikleri getir.
     * English: Get active subscriptions.
     */
    fun getActiveSubscriptions(): Flow<List<SubscriptionEntity>> {
        return subscriptionDao.getActiveSubscriptions()
    }

    /**
     * Turkish: İptal edilmiş abonelikleri getir.
     * English: Get cancelled subscriptions.
     */
    fun getCancelledSubscriptions(): Flow<List<SubscriptionEntity>> {
        return subscriptionDao.getCancelledSubscriptions()
    }

    /**
     * Turkish: Abonelik sayısını getir.
     * English: Get subscription count.
     */
    suspend fun getSubscriptionCount(): Int {
        return subscriptionDao.getSubscriptionCount()
    }

    /**
     * Turkish: Tüm abonelikleri sil.
     * English: Delete all subscriptions.
     */
    suspend fun deleteAllSubscriptions() {
        subscriptionDao.deleteAllSubscriptions()
    }

    /**
     * Turkish: SubscriptionEntity'yi SubscriptionItem'a dönüştür.
     * English: Convert SubscriptionEntity to SubscriptionItem.
     */
    fun mapToSubscriptionItem(entity: SubscriptionEntity): SubscriptionItem {
        return SubscriptionItem(
            serviceName = entity.serviceName,
            status = SubscriptionStatus.valueOf(entity.status),
            emailCount = entity.emailCount,
            lastEmailDate = entity.lastEmailDate,
            cancellationDate = entity.cancellationDate,
            subscriptionStartDate = entity.subscriptionStartDate,
            relatedEmailIds = if (entity.relatedEmailIds.isNotBlank()) {
                entity.relatedEmailIds.split(",")
            } else {
                emptyList()
            }
        )
    }
}
