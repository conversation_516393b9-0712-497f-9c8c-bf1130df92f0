package com.example.abonekaptanmobile.data.local.dao;

import androidx.room.*;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity;
import kotlinx.coroutines.flow.Flow;

/**
 * Turkish: Abonelik veritabanı erişim ne<PERSON>nesi.
 * English: Subscription database access object.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\f\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u000f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u0010H\'J\u0014\u0010\u0012\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u0010H\'J\u000e\u0010\u0013\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0014\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u0010H\'J\u0018\u0010\u0015\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0016\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u00110\u0010H\'J\u0016\u0010\u0018\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u0019\u001a\u00020\u00032\f\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00070\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u0016\u0010\u001c\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006\u001d"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/dao/SubscriptionDao;", "", "deleteAllSubscriptions", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSubscription", "subscription", "Lcom/example/abonekaptanmobile/data/local/entity/SubscriptionEntity;", "(Lcom/example/abonekaptanmobile/data/local/entity/SubscriptionEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteSubscriptionByServiceName", "serviceName", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveSubscriptionCount", "", "getActiveSubscriptions", "Lkotlinx/coroutines/flow/Flow;", "", "getAllSubscriptions", "getCancelledSubscriptionCount", "getCancelledSubscriptions", "getSubscriptionByServiceName", "getSubscriptionCount", "getUnknownSubscriptions", "insertSubscription", "insertSubscriptions", "subscriptions", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateSubscription", "app_release"})
@androidx.room.Dao()
public abstract interface SubscriptionDao {
    
    /**
     * Turkish: Abonelikleri veritabanına ekle.
     * English: Insert subscriptions into database.
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSubscriptions(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity> subscriptions, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tek abonelik ekle.
     * English: Insert single subscription.
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSubscription(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity subscription, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tüm abonelikleri getir.
     * English: Get all subscriptions.
     */
    @androidx.room.Query(value = "SELECT * FROM subscriptions ORDER BY lastEmailDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getAllSubscriptions();
    
    /**
     * Turkish: Aktif abonelikleri getir.
     * English: Get active subscriptions.
     */
    @androidx.room.Query(value = "SELECT * FROM subscriptions WHERE status = \'ACTIVE\' ORDER BY lastEmailDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getActiveSubscriptions();
    
    /**
     * Turkish: İptal edilmiş abonelikleri getir.
     * English: Get cancelled subscriptions.
     */
    @androidx.room.Query(value = "SELECT * FROM subscriptions WHERE status = \'CANCELLED\' ORDER BY lastEmailDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getCancelledSubscriptions();
    
    /**
     * Turkish: Bilinmeyen durumlu abonelikleri getir.
     * English: Get unknown status subscriptions.
     */
    @androidx.room.Query(value = "SELECT * FROM subscriptions WHERE status = \'UNKNOWN\' ORDER BY lastEmailDate DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getUnknownSubscriptions();
    
    /**
     * Turkish: Servis adına göre abonelik getir.
     * English: Get subscription by service name.
     */
    @androidx.room.Query(value = "SELECT * FROM subscriptions WHERE serviceName = :serviceName")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSubscriptionByServiceName(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity> $completion);
    
    /**
     * Turkish: Abonelik sayısını getir.
     * English: Get subscription count.
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM subscriptions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSubscriptionCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * Turkish: Aktif abonelik sayısını getir.
     * English: Get active subscription count.
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM subscriptions WHERE status = \'ACTIVE\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getActiveSubscriptionCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * Turkish: İptal edilmiş abonelik sayısını getir.
     * English: Get cancelled subscription count.
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM subscriptions WHERE status = \'CANCELLED\'")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCancelledSubscriptionCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * Turkish: Abonelik güncelle.
     * English: Update subscription.
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSubscription(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity subscription, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Abonelik sil.
     * English: Delete subscription.
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSubscription(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity subscription, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Servis adına göre abonelik sil.
     * English: Delete subscription by service name.
     */
    @androidx.room.Query(value = "DELETE FROM subscriptions WHERE serviceName = :serviceName")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteSubscriptionByServiceName(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tüm abonelikleri sil.
     * English: Delete all subscriptions.
     */
    @androidx.room.Query(value = "DELETE FROM subscriptions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllSubscriptions(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}