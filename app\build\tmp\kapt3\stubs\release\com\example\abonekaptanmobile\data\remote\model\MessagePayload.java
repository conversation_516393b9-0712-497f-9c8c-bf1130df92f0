package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00004\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001BM\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0003\u0012\u000e\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\b\u0010\t\u001a\u0004\u0018\u00010\n\u0012\u000e\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u0018\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010\u0019\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007H\u00c6\u0003J\u000b\u0010\u001a\u001a\u0004\u0018\u00010\nH\u00c6\u0003J\u0011\u0010\u001b\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u0007H\u00c6\u0003J]\u0010\u001c\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00072\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\n2\u0010\b\u0002\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u001d\u001a\u00020\u001e2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010 \u001a\u00020!H\u00d6\u0001J\t\u0010\"\u001a\u00020\u0003H\u00d6\u0001R\u0018\u0010\t\u001a\u0004\u0018\u00010\n8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000eR\u0018\u0010\u0005\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u001e\u0010\u0006\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0012R\u0018\u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0010R\u0018\u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0010R\u001e\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0000\u0018\u00010\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0012\u00a8\u0006#"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/MessagePayload;", "", "partId", "", "mimeType", "filename", "headers", "", "Lcom/example/abonekaptanmobile/data/remote/model/MessageHeader;", "body", "Lcom/example/abonekaptanmobile/data/remote/model/MessagePartBody;", "parts", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lcom/example/abonekaptanmobile/data/remote/model/MessagePartBody;Ljava/util/List;)V", "getBody", "()Lcom/example/abonekaptanmobile/data/remote/model/MessagePartBody;", "getFilename", "()Ljava/lang/String;", "getHeaders", "()Ljava/util/List;", "getMimeType", "getPartId", "getParts", "component1", "component2", "component3", "component4", "component5", "component6", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
public final class MessagePayload {
    @com.google.gson.annotations.SerializedName(value = "partId")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String partId = null;
    @com.google.gson.annotations.SerializedName(value = "mimeType")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String mimeType = null;
    @com.google.gson.annotations.SerializedName(value = "filename")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String filename = null;
    @com.google.gson.annotations.SerializedName(value = "headers")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageHeader> headers = null;
    @com.google.gson.annotations.SerializedName(value = "body")
    @org.jetbrains.annotations.Nullable()
    private final com.example.abonekaptanmobile.data.remote.model.MessagePartBody body = null;
    @com.google.gson.annotations.SerializedName(value = "parts")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessagePayload> parts = null;
    
    public MessagePayload(@org.jetbrains.annotations.Nullable()
    java.lang.String partId, @org.jetbrains.annotations.Nullable()
    java.lang.String mimeType, @org.jetbrains.annotations.Nullable()
    java.lang.String filename, @org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageHeader> headers, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.MessagePartBody body, @org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.MessagePayload> parts) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getPartId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getMimeType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFilename() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageHeader> getHeaders() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.MessagePartBody getBody() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessagePayload> getParts() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageHeader> component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.MessagePartBody component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.MessagePayload> component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.MessagePayload copy(@org.jetbrains.annotations.Nullable()
    java.lang.String partId, @org.jetbrains.annotations.Nullable()
    java.lang.String mimeType, @org.jetbrains.annotations.Nullable()
    java.lang.String filename, @org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.MessageHeader> headers, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.MessagePartBody body, @org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.MessagePayload> parts) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}