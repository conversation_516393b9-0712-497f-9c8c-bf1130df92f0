package com.example.abonekaptanmobile.data.remote;

import com.example.abonekaptanmobile.data.remote.model.GmailMessage;
import com.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse;
import retrofit2.http.GET;
import retrofit2.http.Path;
import retrofit2.http.Query;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\b\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J\"\u0010\u0002\u001a\u00020\u00032\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0007JB\u0010\b\u001a\u00020\t2\n\b\u0003\u0010\n\u001a\u0004\u0018\u00010\u00052\u0010\b\u0003\u0010\u000b\u001a\n\u0012\u0004\u0012\u00020\u0005\u0018\u00010\f2\b\b\u0003\u0010\r\u001a\u00020\u000e2\n\b\u0003\u0010\u000f\u001a\u0004\u0018\u00010\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0010\u00a8\u0006\u0011"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/GmailApi;", "", "getMessage", "Lcom/example/abonekaptanmobile/data/remote/model/GmailMessage;", "messageId", "", "format", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "listMessages", "Lcom/example/abonekaptanmobile/data/remote/model/GmailMessageListResponse;", "query", "labelIds", "", "maxResults", "", "pageToken", "(Ljava/lang/String;Ljava/util/List;ILjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface GmailApi {
    
    @retrofit2.http.GET(value = "gmail/v1/users/me/messages")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object listMessages(@retrofit2.http.Query(value = "q")
    @org.jetbrains.annotations.Nullable()
    java.lang.String query, @retrofit2.http.Query(value = "labelIds")
    @org.jetbrains.annotations.Nullable()
    java.util.List<java.lang.String> labelIds, @retrofit2.http.Query(value = "maxResults")
    int maxResults, @retrofit2.http.Query(value = "pageToken")
    @org.jetbrains.annotations.Nullable()
    java.lang.String pageToken, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.remote.model.GmailMessageListResponse> $completion);
    
    @retrofit2.http.GET(value = "gmail/v1/users/me/messages/{id}")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getMessage(@retrofit2.http.Path(value = "id")
    @org.jetbrains.annotations.NotNull()
    java.lang.String messageId, @retrofit2.http.Query(value = "format")
    @org.jetbrains.annotations.NotNull()
    java.lang.String format, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.remote.model.GmailMessage> $completion);
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}