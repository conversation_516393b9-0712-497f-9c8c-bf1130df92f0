package com.example.abonekaptanmobile.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@SuppressWarnings({"unchecked", "deprecation"})
public final class SubscriptionDao_Impl implements SubscriptionDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<SubscriptionEntity> __insertionAdapterOfSubscriptionEntity;

  private final EntityDeletionOrUpdateAdapter<SubscriptionEntity> __deletionAdapterOfSubscriptionEntity;

  private final EntityDeletionOrUpdateAdapter<SubscriptionEntity> __updateAdapterOfSubscriptionEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteSubscriptionByServiceName;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSubscriptions;

  public SubscriptionDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfSubscriptionEntity = new EntityInsertionAdapter<SubscriptionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `subscriptions` (`id`,`serviceName`,`status`,`emailCount`,`lastEmailDate`,`cancellationDate`,`subscriptionStartDate`,`relatedEmailIds`) VALUES (nullif(?, 0),?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SubscriptionEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getServiceName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getServiceName());
        }
        if (entity.getStatus() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getStatus());
        }
        statement.bindLong(4, entity.getEmailCount());
        statement.bindLong(5, entity.getLastEmailDate());
        if (entity.getCancellationDate() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getCancellationDate());
        }
        if (entity.getSubscriptionStartDate() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getSubscriptionStartDate());
        }
        if (entity.getRelatedEmailIds() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getRelatedEmailIds());
        }
      }
    };
    this.__deletionAdapterOfSubscriptionEntity = new EntityDeletionOrUpdateAdapter<SubscriptionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `subscriptions` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SubscriptionEntity entity) {
        statement.bindLong(1, entity.getId());
      }
    };
    this.__updateAdapterOfSubscriptionEntity = new EntityDeletionOrUpdateAdapter<SubscriptionEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `subscriptions` SET `id` = ?,`serviceName` = ?,`status` = ?,`emailCount` = ?,`lastEmailDate` = ?,`cancellationDate` = ?,`subscriptionStartDate` = ?,`relatedEmailIds` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SubscriptionEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getServiceName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getServiceName());
        }
        if (entity.getStatus() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getStatus());
        }
        statement.bindLong(4, entity.getEmailCount());
        statement.bindLong(5, entity.getLastEmailDate());
        if (entity.getCancellationDate() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getCancellationDate());
        }
        if (entity.getSubscriptionStartDate() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getSubscriptionStartDate());
        }
        if (entity.getRelatedEmailIds() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getRelatedEmailIds());
        }
        statement.bindLong(9, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteSubscriptionByServiceName = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM subscriptions WHERE serviceName = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllSubscriptions = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM subscriptions";
        return _query;
      }
    };
  }

  @Override
  public Object insertSubscriptions(final List<SubscriptionEntity> subscriptions,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSubscriptionEntity.insert(subscriptions);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object insertSubscription(final SubscriptionEntity subscription,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfSubscriptionEntity.insert(subscription);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteSubscription(final SubscriptionEntity subscription,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfSubscriptionEntity.handle(subscription);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object updateSubscription(final SubscriptionEntity subscription,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfSubscriptionEntity.handle(subscription);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteSubscriptionByServiceName(final String serviceName,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteSubscriptionByServiceName.acquire();
        int _argIndex = 1;
        if (serviceName == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, serviceName);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteSubscriptionByServiceName.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object deleteAllSubscriptions(final Continuation<? super Unit> arg0) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSubscriptions.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSubscriptions.release(_stmt);
        }
      }
    }, arg0);
  }

  @Override
  public Flow<List<SubscriptionEntity>> getAllSubscriptions() {
    final String _sql = "SELECT * FROM subscriptions ORDER BY lastEmailDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subscriptions"}, new Callable<List<SubscriptionEntity>>() {
      @Override
      @NonNull
      public List<SubscriptionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfEmailCount = CursorUtil.getColumnIndexOrThrow(_cursor, "emailCount");
          final int _cursorIndexOfLastEmailDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEmailDate");
          final int _cursorIndexOfCancellationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "cancellationDate");
          final int _cursorIndexOfSubscriptionStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionStartDate");
          final int _cursorIndexOfRelatedEmailIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEmailIds");
          final List<SubscriptionEntity> _result = new ArrayList<SubscriptionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SubscriptionEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final int _tmpEmailCount;
            _tmpEmailCount = _cursor.getInt(_cursorIndexOfEmailCount);
            final long _tmpLastEmailDate;
            _tmpLastEmailDate = _cursor.getLong(_cursorIndexOfLastEmailDate);
            final Long _tmpCancellationDate;
            if (_cursor.isNull(_cursorIndexOfCancellationDate)) {
              _tmpCancellationDate = null;
            } else {
              _tmpCancellationDate = _cursor.getLong(_cursorIndexOfCancellationDate);
            }
            final Long _tmpSubscriptionStartDate;
            if (_cursor.isNull(_cursorIndexOfSubscriptionStartDate)) {
              _tmpSubscriptionStartDate = null;
            } else {
              _tmpSubscriptionStartDate = _cursor.getLong(_cursorIndexOfSubscriptionStartDate);
            }
            final String _tmpRelatedEmailIds;
            if (_cursor.isNull(_cursorIndexOfRelatedEmailIds)) {
              _tmpRelatedEmailIds = null;
            } else {
              _tmpRelatedEmailIds = _cursor.getString(_cursorIndexOfRelatedEmailIds);
            }
            _item = new SubscriptionEntity(_tmpId,_tmpServiceName,_tmpStatus,_tmpEmailCount,_tmpLastEmailDate,_tmpCancellationDate,_tmpSubscriptionStartDate,_tmpRelatedEmailIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SubscriptionEntity>> getActiveSubscriptions() {
    final String _sql = "SELECT * FROM subscriptions WHERE status = 'ACTIVE' ORDER BY lastEmailDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subscriptions"}, new Callable<List<SubscriptionEntity>>() {
      @Override
      @NonNull
      public List<SubscriptionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfEmailCount = CursorUtil.getColumnIndexOrThrow(_cursor, "emailCount");
          final int _cursorIndexOfLastEmailDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEmailDate");
          final int _cursorIndexOfCancellationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "cancellationDate");
          final int _cursorIndexOfSubscriptionStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionStartDate");
          final int _cursorIndexOfRelatedEmailIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEmailIds");
          final List<SubscriptionEntity> _result = new ArrayList<SubscriptionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SubscriptionEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final int _tmpEmailCount;
            _tmpEmailCount = _cursor.getInt(_cursorIndexOfEmailCount);
            final long _tmpLastEmailDate;
            _tmpLastEmailDate = _cursor.getLong(_cursorIndexOfLastEmailDate);
            final Long _tmpCancellationDate;
            if (_cursor.isNull(_cursorIndexOfCancellationDate)) {
              _tmpCancellationDate = null;
            } else {
              _tmpCancellationDate = _cursor.getLong(_cursorIndexOfCancellationDate);
            }
            final Long _tmpSubscriptionStartDate;
            if (_cursor.isNull(_cursorIndexOfSubscriptionStartDate)) {
              _tmpSubscriptionStartDate = null;
            } else {
              _tmpSubscriptionStartDate = _cursor.getLong(_cursorIndexOfSubscriptionStartDate);
            }
            final String _tmpRelatedEmailIds;
            if (_cursor.isNull(_cursorIndexOfRelatedEmailIds)) {
              _tmpRelatedEmailIds = null;
            } else {
              _tmpRelatedEmailIds = _cursor.getString(_cursorIndexOfRelatedEmailIds);
            }
            _item = new SubscriptionEntity(_tmpId,_tmpServiceName,_tmpStatus,_tmpEmailCount,_tmpLastEmailDate,_tmpCancellationDate,_tmpSubscriptionStartDate,_tmpRelatedEmailIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SubscriptionEntity>> getCancelledSubscriptions() {
    final String _sql = "SELECT * FROM subscriptions WHERE status = 'CANCELLED' ORDER BY lastEmailDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subscriptions"}, new Callable<List<SubscriptionEntity>>() {
      @Override
      @NonNull
      public List<SubscriptionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfEmailCount = CursorUtil.getColumnIndexOrThrow(_cursor, "emailCount");
          final int _cursorIndexOfLastEmailDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEmailDate");
          final int _cursorIndexOfCancellationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "cancellationDate");
          final int _cursorIndexOfSubscriptionStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionStartDate");
          final int _cursorIndexOfRelatedEmailIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEmailIds");
          final List<SubscriptionEntity> _result = new ArrayList<SubscriptionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SubscriptionEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final int _tmpEmailCount;
            _tmpEmailCount = _cursor.getInt(_cursorIndexOfEmailCount);
            final long _tmpLastEmailDate;
            _tmpLastEmailDate = _cursor.getLong(_cursorIndexOfLastEmailDate);
            final Long _tmpCancellationDate;
            if (_cursor.isNull(_cursorIndexOfCancellationDate)) {
              _tmpCancellationDate = null;
            } else {
              _tmpCancellationDate = _cursor.getLong(_cursorIndexOfCancellationDate);
            }
            final Long _tmpSubscriptionStartDate;
            if (_cursor.isNull(_cursorIndexOfSubscriptionStartDate)) {
              _tmpSubscriptionStartDate = null;
            } else {
              _tmpSubscriptionStartDate = _cursor.getLong(_cursorIndexOfSubscriptionStartDate);
            }
            final String _tmpRelatedEmailIds;
            if (_cursor.isNull(_cursorIndexOfRelatedEmailIds)) {
              _tmpRelatedEmailIds = null;
            } else {
              _tmpRelatedEmailIds = _cursor.getString(_cursorIndexOfRelatedEmailIds);
            }
            _item = new SubscriptionEntity(_tmpId,_tmpServiceName,_tmpStatus,_tmpEmailCount,_tmpLastEmailDate,_tmpCancellationDate,_tmpSubscriptionStartDate,_tmpRelatedEmailIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Flow<List<SubscriptionEntity>> getUnknownSubscriptions() {
    final String _sql = "SELECT * FROM subscriptions WHERE status = 'UNKNOWN' ORDER BY lastEmailDate DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"subscriptions"}, new Callable<List<SubscriptionEntity>>() {
      @Override
      @NonNull
      public List<SubscriptionEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfEmailCount = CursorUtil.getColumnIndexOrThrow(_cursor, "emailCount");
          final int _cursorIndexOfLastEmailDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEmailDate");
          final int _cursorIndexOfCancellationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "cancellationDate");
          final int _cursorIndexOfSubscriptionStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionStartDate");
          final int _cursorIndexOfRelatedEmailIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEmailIds");
          final List<SubscriptionEntity> _result = new ArrayList<SubscriptionEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SubscriptionEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final int _tmpEmailCount;
            _tmpEmailCount = _cursor.getInt(_cursorIndexOfEmailCount);
            final long _tmpLastEmailDate;
            _tmpLastEmailDate = _cursor.getLong(_cursorIndexOfLastEmailDate);
            final Long _tmpCancellationDate;
            if (_cursor.isNull(_cursorIndexOfCancellationDate)) {
              _tmpCancellationDate = null;
            } else {
              _tmpCancellationDate = _cursor.getLong(_cursorIndexOfCancellationDate);
            }
            final Long _tmpSubscriptionStartDate;
            if (_cursor.isNull(_cursorIndexOfSubscriptionStartDate)) {
              _tmpSubscriptionStartDate = null;
            } else {
              _tmpSubscriptionStartDate = _cursor.getLong(_cursorIndexOfSubscriptionStartDate);
            }
            final String _tmpRelatedEmailIds;
            if (_cursor.isNull(_cursorIndexOfRelatedEmailIds)) {
              _tmpRelatedEmailIds = null;
            } else {
              _tmpRelatedEmailIds = _cursor.getString(_cursorIndexOfRelatedEmailIds);
            }
            _item = new SubscriptionEntity(_tmpId,_tmpServiceName,_tmpStatus,_tmpEmailCount,_tmpLastEmailDate,_tmpCancellationDate,_tmpSubscriptionStartDate,_tmpRelatedEmailIds);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSubscriptionByServiceName(final String serviceName,
      final Continuation<? super SubscriptionEntity> arg1) {
    final String _sql = "SELECT * FROM subscriptions WHERE serviceName = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (serviceName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, serviceName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SubscriptionEntity>() {
      @Override
      @Nullable
      public SubscriptionEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfEmailCount = CursorUtil.getColumnIndexOrThrow(_cursor, "emailCount");
          final int _cursorIndexOfLastEmailDate = CursorUtil.getColumnIndexOrThrow(_cursor, "lastEmailDate");
          final int _cursorIndexOfCancellationDate = CursorUtil.getColumnIndexOrThrow(_cursor, "cancellationDate");
          final int _cursorIndexOfSubscriptionStartDate = CursorUtil.getColumnIndexOrThrow(_cursor, "subscriptionStartDate");
          final int _cursorIndexOfRelatedEmailIds = CursorUtil.getColumnIndexOrThrow(_cursor, "relatedEmailIds");
          final SubscriptionEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            final int _tmpEmailCount;
            _tmpEmailCount = _cursor.getInt(_cursorIndexOfEmailCount);
            final long _tmpLastEmailDate;
            _tmpLastEmailDate = _cursor.getLong(_cursorIndexOfLastEmailDate);
            final Long _tmpCancellationDate;
            if (_cursor.isNull(_cursorIndexOfCancellationDate)) {
              _tmpCancellationDate = null;
            } else {
              _tmpCancellationDate = _cursor.getLong(_cursorIndexOfCancellationDate);
            }
            final Long _tmpSubscriptionStartDate;
            if (_cursor.isNull(_cursorIndexOfSubscriptionStartDate)) {
              _tmpSubscriptionStartDate = null;
            } else {
              _tmpSubscriptionStartDate = _cursor.getLong(_cursorIndexOfSubscriptionStartDate);
            }
            final String _tmpRelatedEmailIds;
            if (_cursor.isNull(_cursorIndexOfRelatedEmailIds)) {
              _tmpRelatedEmailIds = null;
            } else {
              _tmpRelatedEmailIds = _cursor.getString(_cursorIndexOfRelatedEmailIds);
            }
            _result = new SubscriptionEntity(_tmpId,_tmpServiceName,_tmpStatus,_tmpEmailCount,_tmpLastEmailDate,_tmpCancellationDate,_tmpSubscriptionStartDate,_tmpRelatedEmailIds);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getSubscriptionCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM subscriptions";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getActiveSubscriptionCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM subscriptions WHERE status = 'ACTIVE'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getCancelledSubscriptionCount(final Continuation<? super Integer> arg0) {
    final String _sql = "SELECT COUNT(*) FROM subscriptions WHERE status = 'CANCELLED'";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
