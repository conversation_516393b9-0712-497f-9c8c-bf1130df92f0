// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.remote.GroqApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideGroqApiFactory implements Factory<GroqApi> {
  @Override
  public GroqApi get() {
    return provideGroqApi();
  }

  public static AppModule_ProvideGroqApiFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static GroqApi provideGroqApi() {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideGroqApi());
  }

  private static final class InstanceHolder {
    private static final AppModule_ProvideGroqApiFactory INSTANCE = new AppModule_ProvideGroqApiFactory();
  }
}
