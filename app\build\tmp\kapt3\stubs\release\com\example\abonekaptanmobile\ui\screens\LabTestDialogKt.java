package com.example.abonekaptanmobile.ui.screens;

import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.style.TextOverflow;
import androidx.compose.ui.window.DialogProperties;
import com.example.abonekaptanmobile.utils.TestResult;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000 \n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a,\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0007\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u0004H\u0007\u00a8\u0006\u000b"}, d2 = {"LabTestDialog", "", "testResults", "", "Lcom/example/abonekaptanmobile/utils/TestResult;", "isTestingInProgress", "", "onDismiss", "Lkotlin/Function0;", "TestResultCard", "result", "app_release"})
public final class LabTestDialogKt {
    
    @androidx.compose.runtime.Composable()
    public static final void LabTestDialog(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.utils.TestResult> testResults, boolean isTestingInProgress, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss) {
    }
    
    @androidx.compose.runtime.Composable()
    public static final void TestResultCard(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.utils.TestResult result) {
    }
}