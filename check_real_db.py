#!/usr/bin/env python3
import sqlite3
import os

# Güncellenmiş veritabanı dosyası
db_path = "updated_android_db.sqlite"

print(f"Gerçek veritabanı dosyası: {db_path}")
print(f"Dosya var mı: {os.path.exists(db_path)}")
print(f"Dosya boyutu: {os.path.getsize(db_path) if os.path.exists(db_path) else 'N/A'} bytes")

try:
    conn = sqlite3.connect(db_path)
    
    # Tabloları listele
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    print(f"Tablolar: {tables}")
    
    # stage_analysis tablosu var mı kontrol et
    if 'stage_analysis' in tables:
        cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis")
        count = cursor.fetchone()[0]
        print(f"stage_analysis tablosunda kayıt sayısı: {count}")
        
        # İlk 5 kaydı göster
        cursor = conn.execute("SELECT * FROM stage_analysis LIMIT 5")
        rows = cursor.fetchall()
        print(f"İlk 5 kayıt:")
        for i, row in enumerate(rows, 1):
            print(f"  {i}: {row}")
            
        # EVET/HAYIR dağılımı
        cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1")
        yes_count = cursor.fetchone()[0]
        
        cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis WHERE stage1_isSubscriptionCompany = 0")
        no_count = cursor.fetchone()[0]
        
        print(f"\n📊 Analiz Sonuçları:")
        print(f"   ✅ EVET: {yes_count}")
        print(f"   ❌ HAYIR: {no_count}")
        print(f"   📊 Toplam: {count}")
        
        # Örnek şirketler
        cursor = conn.execute("SELECT DISTINCT stage1_companyName FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 LIMIT 10")
        yes_companies = [row[0] for row in cursor.fetchall()]
        print(f"   🏢 EVET şirketleri: {', '.join(yes_companies)}")
        
        cursor = conn.execute("SELECT DISTINCT stage1_companyName FROM stage_analysis WHERE stage1_isSubscriptionCompany = 0 LIMIT 10")
        no_companies = [row[0] for row in cursor.fetchall()]
        print(f"   🏪 HAYIR şirketleri: {', '.join(no_companies)}")
    else:
        print("stage_analysis tablosu bulunamadı!")
    
    conn.close()
    
except Exception as e:
    print(f"Hata: {e}")
