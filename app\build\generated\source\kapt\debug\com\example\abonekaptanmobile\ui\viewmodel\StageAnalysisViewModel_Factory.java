// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.ui.viewmodel;

import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StageAnalysisViewModel_Factory implements Factory<StageAnalysisViewModel> {
  private final Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider;

  public StageAnalysisViewModel_Factory(
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    this.stageAnalysisRepositoryProvider = stageAnalysisRepositoryProvider;
  }

  @Override
  public StageAnalysisViewModel get() {
    return newInstance(stageAnalysisRepositoryProvider.get());
  }

  public static StageAnalysisViewModel_Factory create(
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    return new StageAnalysisViewModel_Factory(stageAnalysisRepositoryProvider);
  }

  public static StageAnalysisViewModel newInstance(
      StageAnalysisRepository stageAnalysisRepository) {
    return new StageAnalysisViewModel(stageAnalysisRepository);
  }
}
