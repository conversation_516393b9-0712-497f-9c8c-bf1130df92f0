#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aşama 1 Email Analiz Sonuçları Web Görüntüleyici
Stage 1 Email Analysis Results Web Viewer
"""

import sqlite3
from flask import Flask, render_template, request, jsonify, Response
from datetime import datetime
import os

app = Flask(__name__)

# Veritabanı dosyası yolu
DB_PATH = "abone_kaptan_db.sqlite"

# Veritabanı dosyasının son değ<PERSON>ş<PERSON> zaman<PERSON>
last_db_modified = 0

def get_db_connection():
    """Veritabanı bağlantısı oluştur"""
    if not os.path.exists(DB_PATH):
        raise FileNotFoundError(f"Veritabanı dosyası bulunamadı: {DB_PATH}")
    
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Dict-like access
    return conn

def format_timestamp(timestamp):
    """Timestamp'i okunabilir formata çevir"""
    if timestamp:
        return datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S')
    return "N/A"

def format_date(date_ms):
    """Email tarihini okunabilir formata çevir"""
    if date_ms:
        return datetime.fromtimestamp(date_ms / 1000).strftime('%Y-%m-%d %H:%M')
    return "N/A"

@app.route('/')
def index():
    """Ana sayfa - Aşama 1 analiz sonuçlarını göster"""
    try:
        conn = get_db_connection()
        
        # Filtreleme parametreleri
        filter_result = request.args.get('filter', 'all')  # all, yes, no
        search_query = request.args.get('search', '').strip()
        
        # Base SQL query
        sql = """
        SELECT 
            id,
            emailIndex,
            emailId,
            domain,
            subject,
            `from`,
            date,
            stage1_isSubscriptionCompany,
            stage1_companyName,
            stage1_companyConfidence,
            stage1_timestamp,
            processingStatus,
            createdAt
        FROM stage_analysis 
        WHERE 1=1
        """
        
        params = []
        
        # Filtreleme uygula
        if filter_result == 'yes':
            sql += " AND stage1_isSubscriptionCompany = 1"
        elif filter_result == 'no':
            sql += " AND stage1_isSubscriptionCompany = 0"
        
        # Arama uygula
        if search_query:
            sql += " AND (domain LIKE ? OR subject LIKE ? OR stage1_companyName LIKE ? OR `from` LIKE ?)"
            search_param = f"%{search_query}%"
            params.extend([search_param, search_param, search_param, search_param])
        
        sql += " ORDER BY emailIndex ASC"
        
        cursor = conn.execute(sql, params)
        results = cursor.fetchall()
        
        # İstatistikler
        stats_cursor = conn.execute("""
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN stage1_isSubscriptionCompany = 1 THEN 1 ELSE 0 END) as yes_count,
            SUM(CASE WHEN stage1_isSubscriptionCompany = 0 THEN 1 ELSE 0 END) as no_count,
            AVG(stage1_companyConfidence) as avg_confidence
        FROM stage_analysis
        """)
        stats = stats_cursor.fetchone()
        
        conn.close()
        
        # Sonuçları işle
        processed_results = []
        for row in results:
            processed_results.append({
                'id': row['id'],
                'emailIndex': row['emailIndex'],
                'emailId': row['emailId'],
                'domain': row['domain'],
                'subject': row['subject'][:100] + '...' if len(row['subject']) > 100 else row['subject'],
                'from': row['from'],
                'date': format_date(row['date']),
                'stage1_isSubscriptionCompany': row['stage1_isSubscriptionCompany'],
                'stage1_companyName': row['stage1_companyName'],
                'stage1_companyConfidence': round(row['stage1_companyConfidence'], 2),
                'stage1_timestamp': format_timestamp(row['stage1_timestamp']),
                'processingStatus': row['processingStatus'],
                'result_text': 'EVET' if row['stage1_isSubscriptionCompany'] else 'HAYIR',
                'result_class': 'success' if row['stage1_isSubscriptionCompany'] else 'danger'
            })
        
        return render_template('index.html', 
                             results=processed_results,
                             stats=stats,
                             filter_result=filter_result,
                             search_query=search_query,
                             total_results=len(processed_results))
        
    except Exception as e:
        return f"Hata: {str(e)}", 500

@app.route('/api/stats')
def api_stats():
    """API endpoint for statistics"""
    try:
        conn = get_db_connection()
        cursor = conn.execute("""
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN stage1_isSubscriptionCompany = 1 THEN 1 ELSE 0 END) as yes_count,
            SUM(CASE WHEN stage1_isSubscriptionCompany = 0 THEN 1 ELSE 0 END) as no_count,
            AVG(stage1_companyConfidence) as avg_confidence,
            MIN(stage1_timestamp) as first_analysis,
            MAX(stage1_timestamp) as last_analysis
        FROM stage_analysis
        """)
        stats = cursor.fetchone()
        conn.close()
        
        return jsonify({
            'total': stats['total'],
            'yes_count': stats['yes_count'],
            'no_count': stats['no_count'],
            'yes_percentage': round((stats['yes_count'] / stats['total']) * 100, 1) if stats['total'] > 0 else 0,
            'no_percentage': round((stats['no_count'] / stats['total']) * 100, 1) if stats['total'] > 0 else 0,
            'avg_confidence': round(stats['avg_confidence'], 2) if stats['avg_confidence'] else 0,
            'first_analysis': format_timestamp(stats['first_analysis']),
            'last_analysis': format_timestamp(stats['last_analysis'])
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/export/csv')
def export_csv():
    """CSV export endpoint"""
    try:
        conn = get_db_connection()

        # Filtreleme parametreleri
        filter_result = request.args.get('filter', 'all')
        search_query = request.args.get('search', '').strip()

        # Base SQL query
        sql = """
        SELECT
            emailIndex,
            emailId,
            domain,
            subject,
            `from`,
            date,
            stage1_isSubscriptionCompany,
            stage1_companyName,
            stage1_companyConfidence,
            stage1_timestamp,
            processingStatus
        FROM stage_analysis
        WHERE 1=1
        """

        params = []

        # Filtreleme uygula
        if filter_result == 'yes':
            sql += " AND stage1_isSubscriptionCompany = 1"
        elif filter_result == 'no':
            sql += " AND stage1_isSubscriptionCompany = 0"

        # Arama uygula
        if search_query:
            sql += " AND (domain LIKE ? OR subject LIKE ? OR stage1_companyName LIKE ? OR `from` LIKE ?)"
            search_param = f"%{search_query}%"
            params.extend([search_param, search_param, search_param, search_param])

        sql += " ORDER BY emailIndex ASC"

        cursor = conn.execute(sql, params)
        results = cursor.fetchall()
        conn.close()

        # CSV içeriği oluştur
        csv_content = "Email Index,Email ID,Domain,Subject,From,Date,Is Subscription,Company Name,Confidence,Analysis Time,Status\n"

        for row in results:
            # CSV için güvenli string oluştur
            def safe_csv_value(value):
                if value is None:
                    return ""
                value_str = str(value)
                # Virgül, tırnak veya yeni satır varsa tırnak içine al
                if ',' in value_str or '"' in value_str or '\n' in value_str:
                    value_str = '"' + value_str.replace('"', '""') + '"'
                return value_str

            csv_content += f"{row['emailIndex']},"
            csv_content += f"{safe_csv_value(row['emailId'])},"
            csv_content += f"{safe_csv_value(row['domain'])},"
            csv_content += f"{safe_csv_value(row['subject'])},"
            csv_content += f"{safe_csv_value(row['from'])},"
            csv_content += f"{format_date(row['date'])},"
            csv_content += f"{'EVET' if row['stage1_isSubscriptionCompany'] else 'HAYIR'},"
            csv_content += f"{safe_csv_value(row['stage1_companyName'])},"
            csv_content += f"{row['stage1_companyConfidence']},"
            csv_content += f"{format_timestamp(row['stage1_timestamp'])},"
            csv_content += f"{safe_csv_value(row['processingStatus'])}\n"

        # CSV dosyası olarak döndür
        response = Response(
            csv_content,
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=asama1_analiz_sonuclari_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            }
        )

        return response

    except Exception as e:
        return f"CSV export hatası: {str(e)}", 500

@app.route('/api/db_status')
def db_status():
    """Veritabanı durumu API endpoint"""
    global last_db_modified

    try:
        if not os.path.exists(DB_PATH):
            return jsonify({
                'exists': False,
                'error': 'Veritabanı dosyası bulunamadı'
            })

        current_modified = os.path.getmtime(DB_PATH)
        file_size = os.path.getsize(DB_PATH)

        # Veritabanı değişti mi kontrol et
        db_changed = current_modified > last_db_modified
        if db_changed:
            last_db_modified = current_modified

        return jsonify({
            'exists': True,
            'last_modified': datetime.fromtimestamp(current_modified).strftime('%Y-%m-%d %H:%M:%S'),
            'file_size': file_size,
            'db_changed': db_changed,
            'timestamp': current_modified
        })

    except Exception as e:
        return jsonify({
            'exists': False,
            'error': str(e)
        })

if __name__ == '__main__':
    print("🚀 Aşama 1 Email Analiz Sonuçları Web Görüntüleyici başlatılıyor...")
    print(f"📁 Veritabanı: {DB_PATH}")
    print("🌐 Web arayüzü: http://localhost:5000")
    print("⏹️  Durdurmak için Ctrl+C")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
