package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: İki aşamalı e-posta analizi için sonuç modeli.
 * English: Result model for two-stage email analysis.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\"\b\u0086\b\u0018\u00002\u00020\u0001BY\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\u0005\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000b\u0012\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u000fJ\t\u0010\u001e\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001f\u001a\u00020\u0005H\u00c6\u0003J\t\u0010 \u001a\u00020\u0005H\u00c6\u0003J\t\u0010!\u001a\u00020\bH\u00c6\u0003J\t\u0010\"\u001a\u00020\u0005H\u00c6\u0003J\t\u0010#\u001a\u00020\u000bH\u00c6\u0003J\u000b\u0010$\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u0010\u0010%\u001a\u0004\u0018\u00010\u000bH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0019J\u000b\u0010&\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003Jn\u0010\'\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00052\b\b\u0002\u0010\u0007\u001a\u00020\b2\b\b\u0002\u0010\t\u001a\u00020\u00052\b\b\u0002\u0010\n\u001a\u00020\u000b2\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000b2\n\b\u0002\u0010\u000e\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001\u00a2\u0006\u0002\u0010(J\u0013\u0010)\u001a\u00020\b2\b\u0010*\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001J\t\u0010,\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\n\u001a\u00020\u000b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\t\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0013R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0013\u0010\f\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0013R\u0015\u0010\r\u001a\u0004\u0018\u00010\u000b\u00a2\u0006\n\n\u0002\u0010\u001a\u001a\u0004\b\u0018\u0010\u0019R\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\u001bR\u0013\u0010\u000e\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0013R\u0011\u0010\u0006\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010\u0013\u00a8\u0006-"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/TwoStageAnalysisResult;", "", "emailIndex", "", "domain", "", "subject", "isSubscriptionCompany", "", "companyName", "companyConfidence", "", "emailType", "emailTypeConfidence", "rawEmailContent", "(ILjava/lang/String;Ljava/lang/String;ZLjava/lang/String;FLjava/lang/String;Ljava/lang/Float;Ljava/lang/String;)V", "getCompanyConfidence", "()F", "getCompanyName", "()Ljava/lang/String;", "getDomain", "getEmailIndex", "()I", "getEmailType", "getEmailTypeConfidence", "()Ljava/lang/Float;", "Ljava/lang/Float;", "()Z", "getRawEmailContent", "getSubject", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "component9", "copy", "(ILjava/lang/String;Ljava/lang/String;ZLjava/lang/String;FLjava/lang/String;Ljava/lang/Float;Ljava/lang/String;)Lcom/example/abonekaptanmobile/data/remote/model/TwoStageAnalysisResult;", "equals", "other", "hashCode", "toString", "app_release"})
public final class TwoStageAnalysisResult {
    private final int emailIndex = 0;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String domain = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String subject = null;
    private final boolean isSubscriptionCompany = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String companyName = null;
    private final float companyConfidence = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String emailType = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Float emailTypeConfidence = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String rawEmailContent = null;
    
    public TwoStageAnalysisResult(int emailIndex, @org.jetbrains.annotations.NotNull()
    java.lang.String domain, @org.jetbrains.annotations.NotNull()
    java.lang.String subject, boolean isSubscriptionCompany, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, float companyConfidence, @org.jetbrains.annotations.Nullable()
    java.lang.String emailType, @org.jetbrains.annotations.Nullable()
    java.lang.Float emailTypeConfidence, @org.jetbrains.annotations.Nullable()
    java.lang.String rawEmailContent) {
        super();
    }
    
    public final int getEmailIndex() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDomain() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSubject() {
        return null;
    }
    
    public final boolean isSubscriptionCompany() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCompanyName() {
        return null;
    }
    
    public final float getCompanyConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getEmailType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float getEmailTypeConfidence() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRawEmailContent() {
        return null;
    }
    
    public final int component1() {
        return 0;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component5() {
        return null;
    }
    
    public final float component6() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Float component8() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component9() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult copy(int emailIndex, @org.jetbrains.annotations.NotNull()
    java.lang.String domain, @org.jetbrains.annotations.NotNull()
    java.lang.String subject, boolean isSubscriptionCompany, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, float companyConfidence, @org.jetbrains.annotations.Nullable()
    java.lang.String emailType, @org.jetbrains.annotations.Nullable()
    java.lang.Float emailTypeConfidence, @org.jetbrains.annotations.Nullable()
    java.lang.String rawEmailContent) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}