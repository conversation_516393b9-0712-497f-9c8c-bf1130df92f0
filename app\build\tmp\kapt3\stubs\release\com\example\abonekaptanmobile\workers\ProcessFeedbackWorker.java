package com.example.abonekaptanmobile.workers;

import android.content.Context;
import android.util.Log;
import androidx.hilt.work.HiltWorker;
import androidx.work.CoroutineWorker;
import androidx.work.WorkerParameters;
import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.local.entity.PatternType;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity;
import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import dagger.assisted.Assisted;
import dagger.assisted.AssistedInject;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0007\u0018\u0000 \u00102\u00020\u0001:\u0001\u0010B3\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0001\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\r\u001a\u00020\u000eH\u0096@\u00a2\u0006\u0002\u0010\u000fR\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/abonekaptanmobile/workers/ProcessFeedbackWorker;", "Landroidx/work/CoroutineWorker;", "appContext", "Landroid/content/Context;", "workerParams", "Landroidx/work/WorkerParameters;", "feedbackRepo", "Lcom/example/abonekaptanmobile/data/repository/FeedbackRepository;", "patternRepo", "Lcom/example/abonekaptanmobile/data/repository/CommunityPatternRepository;", "appDatabase", "Lcom/example/abonekaptanmobile/data/local/AppDatabase;", "(Landroid/content/Context;Landroidx/work/WorkerParameters;Lcom/example/abonekaptanmobile/data/repository/FeedbackRepository;Lcom/example/abonekaptanmobile/data/repository/CommunityPatternRepository;Lcom/example/abonekaptanmobile/data/local/AppDatabase;)V", "doWork", "Landroidx/work/ListenableWorker$Result;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "Companion", "app_release"})
@androidx.hilt.work.HiltWorker()
public final class ProcessFeedbackWorker extends androidx.work.CoroutineWorker {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.FeedbackRepository feedbackRepo = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.CommunityPatternRepository patternRepo = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.local.AppDatabase appDatabase = null;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String WORK_NAME = "ProcessFeedbackWorker";
    private static final int MIN_VOTES_FOR_COMMUNITY_ACTION = 3;
    private static final float COMMUNITY_CONFIDENCE_RATE = 0.67F;
    private static final int DEFAULT_PATTERN_PRIORITY = 30;
    private static final int USER_FEEDBACK_PRIORITY_BOOST = 10;
    private static final int COMMUNITY_APPROVED_PRIORITY = 70;
    private static final int COMMUNITY_REJECTED_PRIORITY = 80;
    private static final int ADMIN_VERIFIED_PRIORITY = 100;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.workers.ProcessFeedbackWorker.Companion Companion = null;
    
    @dagger.assisted.AssistedInject()
    public ProcessFeedbackWorker(@dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    android.content.Context appContext, @dagger.assisted.Assisted()
    @org.jetbrains.annotations.NotNull()
    androidx.work.WorkerParameters workerParams, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.FeedbackRepository feedbackRepo, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.CommunityPatternRepository patternRepo, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.AppDatabase appDatabase) {
        super(null, null);
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.Nullable()
    public java.lang.Object doWork(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super androidx.work.ListenableWorker.Result> $completion) {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u000e"}, d2 = {"Lcom/example/abonekaptanmobile/workers/ProcessFeedbackWorker$Companion;", "", "()V", "ADMIN_VERIFIED_PRIORITY", "", "COMMUNITY_APPROVED_PRIORITY", "COMMUNITY_CONFIDENCE_RATE", "", "COMMUNITY_REJECTED_PRIORITY", "DEFAULT_PATTERN_PRIORITY", "MIN_VOTES_FOR_COMMUNITY_ACTION", "USER_FEEDBACK_PRIORITY_BOOST", "WORK_NAME", "", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}