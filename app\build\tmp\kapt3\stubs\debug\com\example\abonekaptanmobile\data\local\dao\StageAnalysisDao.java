package com.example.abonekaptanmobile.data.local.dao;

import androidx.room.*;
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity;
import kotlinx.coroutines.flow.Flow;

/**
 * Turkish: İki aşamalı analiz sonuçları için DAO.
 * English: DAO for two-stage analysis results.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0012\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0014\u0010\t\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\nH\'J\u0018\u0010\r\u001a\u0004\u0018\u00010\f2\u0006\u0010\u000e\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0010J\u001c\u0010\u0011\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\u0006\u0010\u0012\u001a\u00020\u000fH\'J\u000e\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00160\u000bH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001e\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\'J\u001e\u0010\u001a\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\n2\b\b\u0002\u0010\u0018\u001a\u00020\u0019H\'J\u0014\u0010\u001b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\f0\u000b0\nH\'J\u0016\u0010\u001c\u001a\u00020\u00072\u0006\u0010\u001d\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u001c\u0010\u001f\u001a\u00020\u00032\f\u0010 \u001a\b\u0012\u0004\u0012\u00020\f0\u000bH\u00a7@\u00a2\u0006\u0002\u0010!J\u001e\u0010\"\u001a\u00020\u00032\u0006\u0010#\u001a\u00020\u00072\u0006\u0010$\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010%J6\u0010&\u001a\u00020\u00032\u0006\u0010#\u001a\u00020\u00072\u0006\u0010\'\u001a\u00020\u000f2\u0006\u0010(\u001a\u00020\u00192\u0006\u0010\u0006\u001a\u00020\u00072\u0006\u0010)\u001a\u00020\u000fH\u00a7@\u00a2\u0006\u0002\u0010*\u00a8\u0006+"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/dao/StageAnalysisDao;", "", "deleteAllAnalysisResults", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldAnalysisResults", "timestamp", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllAnalysisResults", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/abonekaptanmobile/data/local/entity/StageAnalysisEntity;", "getAnalysisResultByEmailId", "emailId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAnalysisResultsByCompany", "companyName", "getAnalysisStatistics", "Lcom/example/abonekaptanmobile/data/local/dao/AnalysisStatistics;", "getCompanyStatistics", "Lcom/example/abonekaptanmobile/data/local/dao/CompanyStatistics;", "getEmailsForStage2", "minConfidence", "", "getStage1CompletedEmails", "getStage2CompletedEmails", "insertAnalysisResult", "result", "(Lcom/example/abonekaptanmobile/data/local/entity/StageAnalysisEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertAnalysisResults", "results", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProcessingStatus", "id", "status", "(JLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateStage2Results", "emailType", "confidence", "rawContent", "(JLjava/lang/String;FJLjava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface StageAnalysisDao {
    
    /**
     * Turkish: Tüm analiz sonuçlarını getir.
     * English: Get all analysis results.
     */
    @androidx.room.Query(value = "SELECT * FROM stage_analysis ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getAllAnalysisResults();
    
    /**
     * Turkish: Aşama 1'i tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 1.
     */
    @androidx.room.Query(value = "SELECT * FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 AND stage1_companyConfidence >= :minConfidence ORDER BY date ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getStage1CompletedEmails(float minConfidence);
    
    /**
     * Turkish: Aşama 2'ye geçecek e-postaları getir.
     * English: Get emails ready for stage 2.
     */
    @androidx.room.Query(value = "SELECT * FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 AND stage1_companyConfidence >= :minConfidence AND processingStatus = \'STAGE1_COMPLETED\' ORDER BY date ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getEmailsForStage2(float minConfidence);
    
    /**
     * Turkish: Aşama 2'yi tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 2.
     */
    @androidx.room.Query(value = "SELECT * FROM stage_analysis WHERE processingStatus = \'STAGE2_COMPLETED\' ORDER BY date ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getStage2CompletedEmails();
    
    /**
     * Turkish: Belirli bir şirketin analiz sonuçlarını getir.
     * English: Get analysis results for a specific company.
     */
    @androidx.room.Query(value = "SELECT * FROM stage_analysis WHERE stage1_companyName = :companyName ORDER BY date ASC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity>> getAnalysisResultsByCompany(@org.jetbrains.annotations.NotNull()
    java.lang.String companyName);
    
    /**
     * Turkish: Analiz sonucu ekle.
     * English: Insert analysis result.
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAnalysisResult(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity result, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * Turkish: Birden fazla analiz sonucu ekle.
     * English: Insert multiple analysis results.
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertAnalysisResults(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> results, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Aşama 2 sonuçlarını güncelle.
     * English: Update stage 2 results.
     */
    @androidx.room.Query(value = "\n        UPDATE stage_analysis \n        SET stage2_emailType = :emailType,\n            stage2_emailTypeConfidence = :confidence,\n            stage2_timestamp = :timestamp,\n            stage2_rawContent = :rawContent,\n            processingStatus = \'STAGE2_COMPLETED\'\n        WHERE id = :id\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateStage2Results(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String emailType, float confidence, long timestamp, @org.jetbrains.annotations.NotNull()
    java.lang.String rawContent, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: İşlem durumunu güncelle.
     * English: Update processing status.
     */
    @androidx.room.Query(value = "UPDATE stage_analysis SET processingStatus = :status WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProcessingStatus(long id, @org.jetbrains.annotations.NotNull()
    java.lang.String status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Belirli bir e-posta ID'sine göre analiz sonucunu getir.
     * English: Get analysis result by email ID.
     */
    @androidx.room.Query(value = "SELECT * FROM stage_analysis WHERE emailId = :emailId LIMIT 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalysisResultByEmailId(@org.jetbrains.annotations.NotNull()
    java.lang.String emailId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> $completion);
    
    /**
     * Turkish: Tüm analiz sonuçlarını sil.
     * English: Delete all analysis results.
     */
    @androidx.room.Query(value = "DELETE FROM stage_analysis")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllAnalysisResults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Belirli bir tarihten eski analiz sonuçlarını sil.
     * English: Delete analysis results older than a specific date.
     */
    @androidx.room.Query(value = "DELETE FROM stage_analysis WHERE createdAt < :timestamp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldAnalysisResults(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: İstatistikler için özet bilgi getir.
     * English: Get summary information for statistics.
     */
    @androidx.room.Query(value = "\n        SELECT \n            COUNT(*) as totalEmails,\n            SUM(CASE WHEN stage1_isSubscriptionCompany = 1 THEN 1 ELSE 0 END) as stage1Passed,\n            SUM(CASE WHEN processingStatus = \'STAGE2_COMPLETED\' THEN 1 ELSE 0 END) as stage2Completed,\n            AVG(stage1_companyConfidence) as avgConfidence\n        FROM stage_analysis\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAnalysisStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics> $completion);
    
    /**
     * Turkish: Şirket bazında istatistikler.
     * English: Company-based statistics.
     */
    @androidx.room.Query(value = "\n        SELECT \n            stage1_companyName as companyName,\n            COUNT(*) as emailCount,\n            AVG(stage1_companyConfidence) as avgConfidence,\n            SUM(CASE WHEN processingStatus = \'STAGE2_COMPLETED\' THEN 1 ELSE 0 END) as completedCount\n        FROM stage_analysis \n        WHERE stage1_isSubscriptionCompany = 1 \n        GROUP BY stage1_companyName \n        ORDER BY emailCount DESC\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getCompanyStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.dao.CompanyStatistics>> $completion);
    
    /**
     * Turkish: İki aşamalı analiz sonuçları için DAO.
     * English: DAO for two-stage analysis results.
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}