package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Hugging Face Text Generation API için istek modeli.
 * English: Request model for Hugging Face Text Generation API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\u0019\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\u0006J\t\u0010\u000b\u001a\u00020\u0003H\u00c6\u0003J\u000b\u0010\f\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u001f\u0010\r\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u000e\u001a\u00020\u000f2\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0011\u001a\u00020\u0012H\u00d6\u0001J\t\u0010\u0013\u001a\u00020\u0003H\u00d6\u0001R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010\bR\u0018\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\n\u00a8\u0006\u0014"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceTextGenerationRequest;", "", "inputs", "", "parameters", "Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceTextGenerationParameters;", "(Ljava/lang/String;Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceTextGenerationParameters;)V", "getInputs", "()Ljava/lang/String;", "getParameters", "()Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceTextGenerationParameters;", "component1", "component2", "copy", "equals", "", "other", "hashCode", "", "toString", "app_release"})
public final class HuggingFaceTextGenerationRequest {
    @com.google.gson.annotations.SerializedName(value = "inputs")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String inputs = null;
    @com.google.gson.annotations.SerializedName(value = "parameters")
    @org.jetbrains.annotations.Nullable()
    private final com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters parameters = null;
    
    public HuggingFaceTextGenerationRequest(@org.jetbrains.annotations.NotNull()
    java.lang.String inputs, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters parameters) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getInputs() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters getParameters() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters component2() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest copy(@org.jetbrains.annotations.NotNull()
    java.lang.String inputs, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationParameters parameters) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}