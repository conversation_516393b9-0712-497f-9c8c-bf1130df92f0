package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Hybrid Approach sonuçlarının doğrulama modeli.
 * English: Validation model for Hybrid Approach results.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0017\n\u0002\u0010\b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B=\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\n\u0012\u0006\u0010\u000b\u001a\u00020\f\u00a2\u0006\u0002\u0010\rJ\t\u0010\u0018\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0019\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001a\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001b\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001c\u001a\u00020\u0006H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\nH\u00c6\u0003J\t\u0010\u001e\u001a\u00020\fH\u00c6\u0003JO\u0010\u001f\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\u00062\b\b\u0002\u0010\b\u001a\u00020\u00062\b\b\u0002\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u00c6\u0001J\u0013\u0010 \u001a\u00020\n2\b\u0010!\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\u0006\u0010\"\u001a\u00020\u0003J\t\u0010#\u001a\u00020$H\u00d6\u0001J\u0006\u0010%\u001a\u00020\nJ\u0006\u0010&\u001a\u00020\nJ\t\u0010\'\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u0011\u0010\u000b\u001a\u00020\f\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0011R\u0011\u0010\u0007\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u000fR\u0011\u0010\t\u001a\u00020\n\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0016R\u0011\u0010\b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u000f\u00a8\u0006("}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/HybridValidationResult;", "", "companyName", "", "emailType", "companyConfidence", "", "emailTypeConfidence", "overallConfidence", "isReliable", "", "detailedEmailTypeResult", "Lcom/example/abonekaptanmobile/data/remote/model/DetailedClassificationResult;", "(Ljava/lang/String;Ljava/lang/String;FFFZLcom/example/abonekaptanmobile/data/remote/model/DetailedClassificationResult;)V", "getCompanyConfidence", "()F", "getCompanyName", "()Ljava/lang/String;", "getDetailedEmailTypeResult", "()Lcom/example/abonekaptanmobile/data/remote/model/DetailedClassificationResult;", "getEmailType", "getEmailTypeConfidence", "()Z", "getOverallConfidence", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "equals", "other", "getSummary", "hashCode", "", "isPaidSubscription", "isSubscriptionRelated", "toString", "app_release"})
public final class HybridValidationResult {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String companyName = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String emailType = null;
    private final float companyConfidence = 0.0F;
    private final float emailTypeConfidence = 0.0F;
    private final float overallConfidence = 0.0F;
    private final boolean isReliable = false;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult detailedEmailTypeResult = null;
    
    public HybridValidationResult(@org.jetbrains.annotations.NotNull()
    java.lang.String companyName, @org.jetbrains.annotations.NotNull()
    java.lang.String emailType, float companyConfidence, float emailTypeConfidence, float overallConfidence, boolean isReliable, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult detailedEmailTypeResult) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCompanyName() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getEmailType() {
        return null;
    }
    
    public final float getCompanyConfidence() {
        return 0.0F;
    }
    
    public final float getEmailTypeConfidence() {
        return 0.0F;
    }
    
    public final float getOverallConfidence() {
        return 0.0F;
    }
    
    public final boolean isReliable() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult getDetailedEmailTypeResult() {
        return null;
    }
    
    /**
     * Turkish: Sonucun abonelik ile ilgili olup olmadığını kontrol eder.
     * English: Checks if the result is subscription-related.
     */
    public final boolean isSubscriptionRelated() {
        return false;
    }
    
    /**
     * Turkish: Sonucun ücretli abonelik olup olmadığını tahmin eder.
     * English: Estimates if the result is a paid subscription.
     */
    public final boolean isPaidSubscription() {
        return false;
    }
    
    /**
     * Turkish: Sonuç özeti döndürür.
     * English: Returns a summary of the result.
     */
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getSummary() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    public final float component4() {
        return 0.0F;
    }
    
    public final float component5() {
        return 0.0F;
    }
    
    public final boolean component6() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.HybridValidationResult copy(@org.jetbrains.annotations.NotNull()
    java.lang.String companyName, @org.jetbrains.annotations.NotNull()
    java.lang.String emailType, float companyConfidence, float emailTypeConfidence, float overallConfidence, boolean isReliable, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.model.DetailedClassificationResult detailedEmailTypeResult) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}