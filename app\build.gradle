plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'dagger.hilt.android.plugin' // Proje seviyesindeki versiyonla uyumlu olmalı
    id 'com.google.gms.google-services'
}

// composeBomVersion değişkenini buraya, android bloğ<PERSON>un dı<PERSON>na ta<PERSON>
def composeBomVersion = "2024.02.01" // En güncel stabil Compose BOM versiyonu (Mart 2024 başı)

android {
    namespace 'com.example.abonekaptanmobile'
    compileSdk 34

    defaultConfig {
        applicationId "com.example.abonekaptanmobile"
        minSdk 26
        targetSdk 34
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false // Geliştirme aşamasında false olması hata ayıklamayı kolaylaştırır
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        // Kotlin 1.9.21 için uyumlu Compose Compiler versiyonu (Kontrol edin: developer.android.com/jetpack/androidx/releases/compose-kotlin)
        kotlinCompilerExtensionVersion '1.5.7' // Kotlin 1.9.21 için genellikle 1.5.3 veya 1.5.4 uygundur.
    }

    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
            // META-INF/INDEX.LIST çakışması için çözüm:
            pickFirst 'META-INF/INDEX.LIST'
            pickFirst 'META-INF/DEPENDENCIES'
            // Gerekirse diğer çakışan dosyalar için de pickFirst veya exclude eklenebilir.
            // Örneğin: pickFirst 'META-INF/DEPENDENCIES'
            // pickFirst 'META-INF/io.netty.versions.properties'
        }
    }
}

dependencies {
    // Compose BOM
    implementation platform("androidx.compose:compose-bom:$composeBomVersion")
    androidTestImplementation platform("androidx.compose:compose-bom:$composeBomVersion")

    // Temel AndroidX Kütüphaneleri
    implementation 'androidx.core:core-ktx:1.12.0' // Stabil
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0' // Stabil
    implementation 'androidx.activity:activity-compose:1.8.2' // Stabil
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0") // Veya OkHttp3'ün diğer modülleriyle uyumlu versiyon

    // Jetpack Compose (BOM sayesinde versiyon belirtmeye gerek yok)
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.material:material-icons-core' // BOM yönetir
    implementation 'androidx.compose.material:material-icons-extended' // BOM yönetir

    // Google Sign-In
    implementation 'com.google.android.gms:play-services-auth:21.0.0' // Stabil

    // Retrofit & OkHttp
    implementation 'com.squareup.retrofit2:retrofit:2.9.0' // Stabil, gerekirse güncelleyin
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0' // Stabil

    // Groq API için JSON işleme
    implementation 'com.google.code.gson:gson:2.10.1'

    // Google API Client Kütüphaneleri
    implementation('com.google.api-client:google-api-client-android:2.2.0') { // Stabil
        exclude group: 'org.apache.httpcomponents'
    }
    // Gmail API - Maven Central'dan en güncel STABİL versiyonu kontrol edin!
    implementation('com.google.apis:google-api-services-gmail:v1-rev20220404-2.0.0') { // Bu versiyon daha yaygın ve stabil
        exclude group: 'org.apache.httpcomponents'
    }
    // Google Auth Library (META-INF/INDEX.LIST çakışmasını çözmek için versiyonları uyumlu tutun)
    implementation 'com.google.auth:google-auth-library-oauth2-http:1.23.0' // Stabil
    implementation 'com.google.auth:google-auth-library-credentials:1.23.0' // Stabil

    // Room
    implementation "androidx.room:room-runtime:2.6.1" // Stabil
    kapt "androidx.room:room-compiler:2.6.1"
    implementation "androidx.room:room-ktx:2.6.1"

    // WorkManager
    implementation "androidx.work:work-runtime-ktx:2.9.0" // Stabil

    // Hilt (Proje seviyesi plugin versiyonuyla aynı olmalı)
    implementation "com.google.dagger:hilt-android:2.49" // Proje seviyesindeki plugin ile aynı versiyonu kullanın (örn: 2.49)
    kapt "com.google.dagger:hilt-compiler:2.49"     // Proje seviyesindeki plugin ile aynı versiyonu kullanın (örn: 2.49)
    implementation 'androidx.hilt:hilt-navigation-compose:1.1.0' // Stabil (veya 1.2.0-beta01)
    implementation "androidx.hilt:hilt-work:1.1.0" // Stabil (veya 1.2.0-beta01)
    kapt "androidx.hilt:hilt-compiler:1.1.0"      // (androidx olan, hilt-work ve hilt-navigation-compose için)

    // ViewModel
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0" // Stabil
    implementation "androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0" // Stabil
    implementation "androidx.lifecycle:lifecycle-runtime-compose:2.7.0" // Stabil

    // Test Bağımlılıkları
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4' // BOM yönetir
    debugImplementation 'androidx.compose.ui:ui-tooling' // BOM yönetir
    debugImplementation 'androidx.compose.ui:ui-test-manifest' // BOM yönetir
}

kapt {
    correctErrorTypes true
}