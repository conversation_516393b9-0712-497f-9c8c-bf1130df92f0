package com.example.abonekaptanmobile.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.EntityUpsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionPatternEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@SuppressWarnings({"unchecked", "deprecation"})
public final class CommunityPatternDao_Impl implements CommunityPatternDao {
  private final RoomDatabase __db;

  private final EntityUpsertionAdapter<SubscriptionPatternEntity> __upsertionAdapterOfSubscriptionPatternEntity;

  public CommunityPatternDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__upsertionAdapterOfSubscriptionPatternEntity = new EntityUpsertionAdapter<SubscriptionPatternEntity>(new EntityInsertionAdapter<SubscriptionPatternEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT INTO `subscription_patterns` (`id`,`serviceName`,`regexPattern`,`source`,`approvedCount`,`rejectedCount`,`isSubscription`,`isTrustedSenderDomain`,`patternType`,`priority`,`createdAt`,`updatedAt`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SubscriptionPatternEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getServiceName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getServiceName());
        }
        if (entity.getRegexPattern() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getRegexPattern());
        }
        if (entity.getSource() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSource());
        }
        statement.bindLong(5, entity.getApprovedCount());
        statement.bindLong(6, entity.getRejectedCount());
        final int _tmp = entity.isSubscription() ? 1 : 0;
        statement.bindLong(7, _tmp);
        final int _tmp_1 = entity.isTrustedSenderDomain() ? 1 : 0;
        statement.bindLong(8, _tmp_1);
        if (entity.getPatternType() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getPatternType());
        }
        statement.bindLong(10, entity.getPriority());
        statement.bindLong(11, entity.getCreatedAt());
        statement.bindLong(12, entity.getUpdatedAt());
      }
    }, new EntityDeletionOrUpdateAdapter<SubscriptionPatternEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE `subscription_patterns` SET `id` = ?,`serviceName` = ?,`regexPattern` = ?,`source` = ?,`approvedCount` = ?,`rejectedCount` = ?,`isSubscription` = ?,`isTrustedSenderDomain` = ?,`patternType` = ?,`priority` = ?,`createdAt` = ?,`updatedAt` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final SubscriptionPatternEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getServiceName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getServiceName());
        }
        if (entity.getRegexPattern() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getRegexPattern());
        }
        if (entity.getSource() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getSource());
        }
        statement.bindLong(5, entity.getApprovedCount());
        statement.bindLong(6, entity.getRejectedCount());
        final int _tmp = entity.isSubscription() ? 1 : 0;
        statement.bindLong(7, _tmp);
        final int _tmp_1 = entity.isTrustedSenderDomain() ? 1 : 0;
        statement.bindLong(8, _tmp_1);
        if (entity.getPatternType() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getPatternType());
        }
        statement.bindLong(10, entity.getPriority());
        statement.bindLong(11, entity.getCreatedAt());
        statement.bindLong(12, entity.getUpdatedAt());
        statement.bindLong(13, entity.getId());
      }
    });
  }

  @Override
  public Object upsertPattern(final SubscriptionPatternEntity pattern,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __upsertionAdapterOfSubscriptionPatternEntity.upsert(pattern);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object getReliableSubscriptionPatterns(
      final Continuation<? super List<SubscriptionPatternEntity>> arg0) {
    final String _sql = "SELECT * FROM subscription_patterns WHERE isSubscription = 1 AND source = 'default_verified' ORDER BY priority DESC, approvedCount DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SubscriptionPatternEntity>>() {
      @Override
      @NonNull
      public List<SubscriptionPatternEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfRegexPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "regexPattern");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfApprovedCount = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedCount");
          final int _cursorIndexOfRejectedCount = CursorUtil.getColumnIndexOrThrow(_cursor, "rejectedCount");
          final int _cursorIndexOfIsSubscription = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscription");
          final int _cursorIndexOfIsTrustedSenderDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrustedSenderDomain");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<SubscriptionPatternEntity> _result = new ArrayList<SubscriptionPatternEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SubscriptionPatternEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpRegexPattern;
            if (_cursor.isNull(_cursorIndexOfRegexPattern)) {
              _tmpRegexPattern = null;
            } else {
              _tmpRegexPattern = _cursor.getString(_cursorIndexOfRegexPattern);
            }
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final int _tmpApprovedCount;
            _tmpApprovedCount = _cursor.getInt(_cursorIndexOfApprovedCount);
            final int _tmpRejectedCount;
            _tmpRejectedCount = _cursor.getInt(_cursorIndexOfRejectedCount);
            final boolean _tmpIsSubscription;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscription);
            _tmpIsSubscription = _tmp != 0;
            final boolean _tmpIsTrustedSenderDomain;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsTrustedSenderDomain);
            _tmpIsTrustedSenderDomain = _tmp_1 != 0;
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new SubscriptionPatternEntity(_tmpId,_tmpServiceName,_tmpRegexPattern,_tmpSource,_tmpApprovedCount,_tmpRejectedCount,_tmpIsSubscription,_tmpIsTrustedSenderDomain,_tmpPatternType,_tmpPriority,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @Override
  public Object getNonSubscriptionPatterns(final int minRejectionVotes,
      final Continuation<? super List<SubscriptionPatternEntity>> arg1) {
    final String _sql = "SELECT * FROM subscription_patterns WHERE isSubscription = 0 AND (source = 'community_rejected' OR (rejectedCount > ? AND approvedCount < rejectedCount)) ORDER BY rejectedCount DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, minRejectionVotes);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<SubscriptionPatternEntity>>() {
      @Override
      @NonNull
      public List<SubscriptionPatternEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfRegexPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "regexPattern");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfApprovedCount = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedCount");
          final int _cursorIndexOfRejectedCount = CursorUtil.getColumnIndexOrThrow(_cursor, "rejectedCount");
          final int _cursorIndexOfIsSubscription = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscription");
          final int _cursorIndexOfIsTrustedSenderDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrustedSenderDomain");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final List<SubscriptionPatternEntity> _result = new ArrayList<SubscriptionPatternEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final SubscriptionPatternEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpRegexPattern;
            if (_cursor.isNull(_cursorIndexOfRegexPattern)) {
              _tmpRegexPattern = null;
            } else {
              _tmpRegexPattern = _cursor.getString(_cursorIndexOfRegexPattern);
            }
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final int _tmpApprovedCount;
            _tmpApprovedCount = _cursor.getInt(_cursorIndexOfApprovedCount);
            final int _tmpRejectedCount;
            _tmpRejectedCount = _cursor.getInt(_cursorIndexOfRejectedCount);
            final boolean _tmpIsSubscription;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscription);
            _tmpIsSubscription = _tmp != 0;
            final boolean _tmpIsTrustedSenderDomain;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsTrustedSenderDomain);
            _tmpIsTrustedSenderDomain = _tmp_1 != 0;
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _item = new SubscriptionPatternEntity(_tmpId,_tmpServiceName,_tmpRegexPattern,_tmpSource,_tmpApprovedCount,_tmpRejectedCount,_tmpIsSubscription,_tmpIsTrustedSenderDomain,_tmpPatternType,_tmpPriority,_tmpCreatedAt,_tmpUpdatedAt);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @Override
  public Object getPatternByServiceName(final String serviceName,
      final Continuation<? super SubscriptionPatternEntity> arg1) {
    final String _sql = "SELECT * FROM subscription_patterns WHERE serviceName = ? COLLATE NOCASE LIMIT 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (serviceName == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, serviceName);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<SubscriptionPatternEntity>() {
      @Override
      @Nullable
      public SubscriptionPatternEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfRegexPattern = CursorUtil.getColumnIndexOrThrow(_cursor, "regexPattern");
          final int _cursorIndexOfSource = CursorUtil.getColumnIndexOrThrow(_cursor, "source");
          final int _cursorIndexOfApprovedCount = CursorUtil.getColumnIndexOrThrow(_cursor, "approvedCount");
          final int _cursorIndexOfRejectedCount = CursorUtil.getColumnIndexOrThrow(_cursor, "rejectedCount");
          final int _cursorIndexOfIsSubscription = CursorUtil.getColumnIndexOrThrow(_cursor, "isSubscription");
          final int _cursorIndexOfIsTrustedSenderDomain = CursorUtil.getColumnIndexOrThrow(_cursor, "isTrustedSenderDomain");
          final int _cursorIndexOfPatternType = CursorUtil.getColumnIndexOrThrow(_cursor, "patternType");
          final int _cursorIndexOfPriority = CursorUtil.getColumnIndexOrThrow(_cursor, "priority");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final SubscriptionPatternEntity _result;
          if (_cursor.moveToFirst()) {
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpRegexPattern;
            if (_cursor.isNull(_cursorIndexOfRegexPattern)) {
              _tmpRegexPattern = null;
            } else {
              _tmpRegexPattern = _cursor.getString(_cursorIndexOfRegexPattern);
            }
            final String _tmpSource;
            if (_cursor.isNull(_cursorIndexOfSource)) {
              _tmpSource = null;
            } else {
              _tmpSource = _cursor.getString(_cursorIndexOfSource);
            }
            final int _tmpApprovedCount;
            _tmpApprovedCount = _cursor.getInt(_cursorIndexOfApprovedCount);
            final int _tmpRejectedCount;
            _tmpRejectedCount = _cursor.getInt(_cursorIndexOfRejectedCount);
            final boolean _tmpIsSubscription;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsSubscription);
            _tmpIsSubscription = _tmp != 0;
            final boolean _tmpIsTrustedSenderDomain;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfIsTrustedSenderDomain);
            _tmpIsTrustedSenderDomain = _tmp_1 != 0;
            final String _tmpPatternType;
            if (_cursor.isNull(_cursorIndexOfPatternType)) {
              _tmpPatternType = null;
            } else {
              _tmpPatternType = _cursor.getString(_cursorIndexOfPatternType);
            }
            final int _tmpPriority;
            _tmpPriority = _cursor.getInt(_cursorIndexOfPriority);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final long _tmpUpdatedAt;
            _tmpUpdatedAt = _cursor.getLong(_cursorIndexOfUpdatedAt);
            _result = new SubscriptionPatternEntity(_tmpId,_tmpServiceName,_tmpRegexPattern,_tmpSource,_tmpApprovedCount,_tmpRejectedCount,_tmpIsSubscription,_tmpIsTrustedSenderDomain,_tmpPatternType,_tmpPriority,_tmpCreatedAt,_tmpUpdatedAt);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg1);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
