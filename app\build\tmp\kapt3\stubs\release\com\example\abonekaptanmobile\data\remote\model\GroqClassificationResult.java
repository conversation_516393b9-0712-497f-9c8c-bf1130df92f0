package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Groq API'den dönen sınıflandırma sonucu.
 * English: Classification result returned from Groq API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u0000 \u00172\u00020\u0001:\u0001\u0017B!\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\u0002\u0010\u0007J\t\u0010\r\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u000e\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u000f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J)\u0010\u0010\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0003H\u00c6\u0001J\u0013\u0010\u0011\u001a\u00020\u00122\b\u0010\u0013\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0014\u001a\u00020\u0015H\u00d6\u0001J\t\u0010\u0016\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\tR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u000b\u00a8\u0006\u0018"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GroqClassificationResult;", "", "label", "", "confidence", "", "reasoning", "(Ljava/lang/String;FLjava/lang/String;)V", "getConfidence", "()F", "getLabel", "()Ljava/lang/String;", "getReasoning", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "Companion", "app_release"})
public final class GroqClassificationResult {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String label = null;
    private final float confidence = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String reasoning = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.data.remote.model.GroqClassificationResult.Companion Companion = null;
    
    public GroqClassificationResult(@org.jetbrains.annotations.NotNull()
    java.lang.String label, float confidence, @org.jetbrains.annotations.Nullable()
    java.lang.String reasoning) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLabel() {
        return null;
    }
    
    public final float getConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getReasoning() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final float component2() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.GroqClassificationResult copy(@org.jetbrains.annotations.NotNull()
    java.lang.String label, float confidence, @org.jetbrains.annotations.Nullable()
    java.lang.String reasoning) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0010\u000e\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bJ\u001e\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\t2\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002J\u001e\u0010\f\u001a\u00020\u00042\u0006\u0010\r\u001a\u00020\t2\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002\u00a8\u0006\u000e"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GroqClassificationResult$Companion;", "", "()V", "fromGroqResponse", "Lcom/example/abonekaptanmobile/data/remote/model/GroqClassificationResult;", "response", "Lcom/example/abonekaptanmobile/data/remote/model/GroqChatResponse;", "candidateLabels", "", "", "parseJsonResponse", "jsonStr", "parseTextResponse", "content", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        /**
         * Turkish: Groq yanıtından sınıflandırma sonucu çıkarır.
         * English: Extracts classification result from Groq response.
         */
        @org.jetbrains.annotations.NotNull()
        public final com.example.abonekaptanmobile.data.remote.model.GroqClassificationResult fromGroqResponse(@org.jetbrains.annotations.NotNull()
        com.example.abonekaptanmobile.data.remote.model.GroqChatResponse response, @org.jetbrains.annotations.NotNull()
        java.util.List<java.lang.String> candidateLabels) {
            return null;
        }
        
        private final com.example.abonekaptanmobile.data.remote.model.GroqClassificationResult parseJsonResponse(java.lang.String jsonStr, java.util.List<java.lang.String> candidateLabels) {
            return null;
        }
        
        private final com.example.abonekaptanmobile.data.remote.model.GroqClassificationResult parseTextResponse(java.lang.String content, java.util.List<java.lang.String> candidateLabels) {
            return null;
        }
    }
}