// file: app/java/com/example/abonekaptanmobile/data/remote/model/HuggingFaceModels.kt
package com.example.abonekaptanmobile.data.remote.model

import com.google.gson.annotations.SerializedName

/**
 * Turkish: Hugging Face API'sine gönderilecek istek modeli.
 * English: Request model to be sent to Hugging Face API.
 */
data class HuggingFaceRequest(
    @SerializedName("inputs") val inputs: String,
    @SerializedName("parameters") val parameters: HuggingFaceParameters
)

/**
 * Turkish: Hugging Face API isteği için parametreler.
 * English: Parameters for Hugging Face API request.
 */
data class HuggingFaceParameters(
    @SerializedName("candidate_labels") val candidateLabels: List<String>
)

/**
 * Turkish: Hugging Face API'sinden dönen yanıt modeli.
 * English: Response model returned from Hugging Face API.
 */
data class HuggingFaceResponse(
    @SerializedName("sequence") val sequence: String,
    @SerializedName("labels") val labels: List<String>,
    @SerializedName("scores") val scores: List<Float>
)

/**
 * Turkish: Hugging Face API'sinden dönen sınıflandırma sonucu.
 * English: Classification result returned from Hugging Face API.
 */
data class ClassificationResult(
    val label: String,
    val score: Float
) {
    companion object {
        fun fromResponse(response: HuggingFaceResponse): List<ClassificationResult> {
            return response.labels.zip(response.scores) { label, score ->
                ClassificationResult(label, score)
            }
        }
    }
}

/**
 * Turkish: Hugging Face API'sinden dönen detaylı sınıflandırma sonucu.
 * English: Detailed classification result returned from Hugging Face API.
 */
data class DetailedClassificationResult(
    val primaryLabel: String,
    val primaryScore: Float,
    val allResults: List<ClassificationResult>
) {
    /**
     * Turkish: Belirli bir etiketin skorunu döndürür.
     * English: Returns the score for a specific label.
     */
    fun getScoreForLabel(label: String): Float {
        return allResults.find { it.label == label }?.score ?: 0f
    }

    /**
     * Turkish: Belirli bir eşik değerinin üzerinde olan etiketleri döndürür.
     * English: Returns labels that have a score above a certain threshold.
     */
    fun getLabelsAboveThreshold(threshold: Float): List<String> {
        return allResults.filter { it.score >= threshold }.map { it.label }
    }

    /**
     * Turkish: Belirli bir etiketin belirli bir eşik değerinin üzerinde olup olmadığını kontrol eder.
     * English: Checks if a specific label has a score above a certain threshold.
     */
    fun isLabelAboveThreshold(label: String, threshold: Float): Boolean {
        return getScoreForLabel(label) >= threshold
    }
}

/**
 * Turkish: Hybrid Approach sonuçlarının doğrulama modeli.
 * English: Validation model for Hybrid Approach results.
 */
data class HybridValidationResult(
    val companyName: String,                    // Tespit edilen şirket adı
    val emailType: String,                      // Tespit edilen email türü
    val companyConfidence: Float,               // Şirket tespiti güven skoru
    val emailTypeConfidence: Float,             // Email türü tespiti güven skoru
    val overallConfidence: Float,               // Genel güven skoru
    val isReliable: Boolean,                    // Sonucun güvenilir olup olmadığı
    val detailedEmailTypeResult: DetailedClassificationResult  // Detaylı email türü sonuçları
) {
    /**
     * Turkish: Sonucun abonelik ile ilgili olup olmadığını kontrol eder.
     * English: Checks if the result is subscription-related.
     */
    fun isSubscriptionRelated(): Boolean {
        return emailType in listOf(
            "subscription_start",
            "subscription_cancel",
            "subscription_renewal",
            "payment_confirmation",
            "welcome_message",
            "billing_notification"
        )
    }

    /**
     * Turkish: Sonucun ücretli abonelik olup olmadığını tahmin eder.
     * English: Estimates if the result is a paid subscription.
     */
    fun isPaidSubscription(): Boolean {
        return emailType in listOf(
            "subscription_start",
            "payment_confirmation",
            "billing_notification",
            "subscription_renewal"
        ) && companyName != "other"
    }

    /**
     * Turkish: Sonuç özeti döndürür.
     * English: Returns a summary of the result.
     */
    fun getSummary(): String {
        return "Company: $companyName (${String.format("%.2f", companyConfidence)}), " +
                "Type: $emailType (${String.format("%.2f", emailTypeConfidence)}), " +
                "Overall: ${String.format("%.2f", overallConfidence)}, " +
                "Reliable: $isReliable"
    }
}

/**
 * Turkish: Replicate API için istek modeli.
 * English: Request model for Replicate API.
 */
data class LlamaRequest(
    @SerializedName("version") val version: String = "2c1608e18606fad2812020dc541930f2d0495ce32eee50074220b87300bc16e1",
    @SerializedName("input") val input: ReplicateInput
)

/**
 * Turkish: Replicate API için input modeli.
 * English: Input model for Replicate API.
 */
data class ReplicateInput(
    @SerializedName("prompt") val prompt: String,
    @SerializedName("max_new_tokens") val maxNewTokens: Int = 100,
    @SerializedName("temperature") val temperature: Float = 0.1f,
    @SerializedName("do_sample") val doSample: Boolean = false,
    @SerializedName("top_p") val topP: Float = 0.9f,
    @SerializedName("top_k") val topK: Int = 50
)

/**
 * Turkish: Llama modeli için parametreler (eski sistem - backward compatibility).
 * English: Parameters for Llama model (legacy system - backward compatibility).
 */
data class LlamaParameters(
    @SerializedName("max_new_tokens") val maxNewTokens: Int = 50,
    @SerializedName("temperature") val temperature: Float = 0.1f,
    @SerializedName("do_sample") val doSample: Boolean = false,
    @SerializedName("return_full_text") val returnFullText: Boolean = false
)

/**
 * Turkish: Replicate API'den dönen yanıt (eski sistem - backward compatibility).
 * English: Response from Replicate API (legacy system - backward compatibility).
 */
data class LlamaResponse(
    @SerializedName("output") val output: List<String>? = null,
    @SerializedName("error") val error: String? = null,
    @SerializedName("status") val status: String? = null
) {
    val generatedText: String?
        get() = output?.joinToString("")

    companion object {
        fun fromArrayResponse(responses: List<LlamaResponse>): LlamaResponse {
            return responses.firstOrNull() ?: LlamaResponse(error = "Empty response")
        }
    }
}

/**
 * Turkish: Hugging Face Text Generation API için istek modeli.
 * English: Request model for Hugging Face Text Generation API.
 */
data class HuggingFaceTextGenerationRequest(
    @SerializedName("inputs") val inputs: String,
    @SerializedName("parameters") val parameters: HuggingFaceTextGenerationParameters? = null
)

/**
 * Turkish: Hugging Face Text Generation API için parametreler.
 * English: Parameters for Hugging Face Text Generation API.
 */
data class HuggingFaceTextGenerationParameters(
    @SerializedName("max_new_tokens") val maxNewTokens: Int = 100,
    @SerializedName("temperature") val temperature: Float = 0.1f,
    @SerializedName("do_sample") val doSample: Boolean = false,
    @SerializedName("top_p") val topP: Float = 0.9f,
    @SerializedName("top_k") val topK: Int = 50,
    @SerializedName("return_full_text") val returnFullText: Boolean = false
)

/**
 * Turkish: Hugging Face Text Generation API'den dönen yanıt.
 * English: Response from Hugging Face Text Generation API.
 */
data class HuggingFaceTextGenerationResponse(
    @SerializedName("generated_text") val generatedText: String? = null,
    @SerializedName("error") val error: String? = null
) {
    companion object {
        fun fromArrayResponse(responses: List<HuggingFaceTextGenerationResponse>): HuggingFaceTextGenerationResponse {
            return responses.firstOrNull() ?: HuggingFaceTextGenerationResponse(error = "Empty response")
        }
    }
}

/**
 * Turkish: İki aşamalı e-posta analizi için sonuç modeli.
 * English: Result model for two-stage email analysis.
 */
data class TwoStageAnalysisResult(
    val emailIndex: Int,                    // E-postanın sıra numarası
    val domain: String,                     // E-posta domain'i
    val subject: String,                    // E-posta başlığı
    val isSubscriptionCompany: Boolean,     // Ücretli abonelik şirketi mi?
    val companyName: String,                // Tespit edilen şirket adı
    val companyConfidence: Float,           // Şirket tespiti güven skoru
    val emailType: String? = null,          // E-posta türü (2. aşamada doldurulur)
    val emailTypeConfidence: Float? = null, // E-posta türü güven skoru (2. aşamada doldurulur)
    val rawEmailContent: String? = null     // Ham e-posta içeriği (2. aşamada kullanılır)
)

/**
 * Turkish: Final abonelik durumu.
 * English: Final subscription status.
 */
data class FinalSubscriptionStatus(
    val companyName: String,                // Şirket adı
    val status: String,                     // "subscription_start", "subscription_cancel", "none"
    val lastEmailDate: Long,                // Son e-posta tarihi
    val emailCount: Int,                    // Bu şirketten gelen toplam e-posta sayısı
    val confidence: Float                   // Güven skoru
)