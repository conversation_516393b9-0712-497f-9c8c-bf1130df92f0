package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Groq API choice modeli.
 * English: Groq API choice model.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u000e\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001B)\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003\u00a2\u0006\u0002\u0010\fJ\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J\u000b\u0010\u0012\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J2\u0010\u0013\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001\u00a2\u0006\u0002\u0010\u0014J\u0013\u0010\u0015\u001a\u00020\u00162\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0007H\u00d6\u0001R\u0018\u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u001a\u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\r\u001a\u0004\b\u000b\u0010\fR\u0018\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\u00a8\u0006\u001a"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GroqChoice;", "", "index", "", "message", "Lcom/example/abonekaptanmobile/data/remote/model/GroqMessage;", "finishReason", "", "(Ljava/lang/Integer;Lcom/example/abonekaptanmobile/data/remote/model/GroqMessage;Ljava/lang/String;)V", "getFinishReason", "()Ljava/lang/String;", "getIndex", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getMessage", "()Lcom/example/abonekaptanmobile/data/remote/model/GroqMessage;", "component1", "component2", "component3", "copy", "(Ljava/lang/Integer;Lcom/example/abonekaptanmobile/data/remote/model/GroqMessage;Ljava/lang/String;)Lcom/example/abonekaptanmobile/data/remote/model/GroqChoice;", "equals", "", "other", "hashCode", "toString", "app_debug"})
public final class GroqChoice {
    @com.google.gson.annotations.SerializedName(value = "index")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer index = null;
    @com.google.gson.annotations.SerializedName(value = "message")
    @org.jetbrains.annotations.Nullable()
    private final com.example.abonekaptanmobile.data.remote.model.GroqMessage message = null;
    @com.google.gson.annotations.SerializedName(value = "finish_reason")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String finishReason = null;
    
    public GroqChoice(@org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.GroqMessage message, @org.jetbrains.annotations.Nullable()
    java.lang.String finishReason) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getIndex() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.GroqMessage getMessage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFinishReason() {
        return null;
    }
    
    public GroqChoice() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.GroqMessage component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.GroqChoice copy(@org.jetbrains.annotations.Nullable()
    java.lang.Integer index, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.GroqMessage message, @org.jetbrains.annotations.Nullable()
    java.lang.String finishReason) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}