package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Groq API'den dönen yanıt modeli.
 * English: Response model returned from Groq API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0018\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B_\u0012\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u0006\u0012\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t\u0012\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f\u0012\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000e\u00a2\u0006\u0002\u0010\u000fJ\u000b\u0010\u001d\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\u001e\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010\u001f\u001a\u0004\u0018\u00010\u0006H\u00c6\u0003\u00a2\u0006\u0002\u0010\u0013J\u000b\u0010 \u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0011\u0010!\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\tH\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\fH\u00c6\u0003J\u000b\u0010#\u001a\u0004\u0018\u00010\u000eH\u00c6\u0003Jh\u0010$\u001a\u00020\u00002\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0005\u001a\u0004\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\u0010\b\u0002\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t2\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\f2\n\b\u0002\u0010\r\u001a\u0004\u0018\u00010\u000eH\u00c6\u0001\u00a2\u0006\u0002\u0010%J\u0013\u0010&\u001a\u00020\'2\b\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010)\u001a\u00020*H\u00d6\u0001J\t\u0010+\u001a\u00020\u0003H\u00d6\u0001R\u001e\u0010\b\u001a\n\u0012\u0004\u0012\u00020\n\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0011R\u001a\u0010\u0005\u001a\u0004\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\u0014\u001a\u0004\b\u0012\u0010\u0013R\u0018\u0010\r\u001a\u0004\u0018\u00010\u000e8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u0018\u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0017\u0010\u0018R\u0018\u0010\u0007\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0019\u0010\u0018R\u0018\u0010\u0004\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0018R\u0018\u0010\u000b\u001a\u0004\u0018\u00010\f8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u001c\u00a8\u0006,"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GroqChatResponse;", "", "id", "", "objectType", "created", "", "model", "choices", "", "Lcom/example/abonekaptanmobile/data/remote/model/GroqChoice;", "usage", "Lcom/example/abonekaptanmobile/data/remote/model/GroqUsage;", "error", "Lcom/example/abonekaptanmobile/data/remote/model/GroqError;", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/util/List;Lcom/example/abonekaptanmobile/data/remote/model/GroqUsage;Lcom/example/abonekaptanmobile/data/remote/model/GroqError;)V", "getChoices", "()Ljava/util/List;", "getCreated", "()Ljava/lang/Long;", "Ljava/lang/Long;", "getError", "()Lcom/example/abonekaptanmobile/data/remote/model/GroqError;", "getId", "()Ljava/lang/String;", "getModel", "getObjectType", "getUsage", "()Lcom/example/abonekaptanmobile/data/remote/model/GroqUsage;", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/util/List;Lcom/example/abonekaptanmobile/data/remote/model/GroqUsage;Lcom/example/abonekaptanmobile/data/remote/model/GroqError;)Lcom/example/abonekaptanmobile/data/remote/model/GroqChatResponse;", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class GroqChatResponse {
    @com.google.gson.annotations.SerializedName(value = "id")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String id = null;
    @com.google.gson.annotations.SerializedName(value = "object")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String objectType = null;
    @com.google.gson.annotations.SerializedName(value = "created")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Long created = null;
    @com.google.gson.annotations.SerializedName(value = "model")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String model = null;
    @com.google.gson.annotations.SerializedName(value = "choices")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.GroqChoice> choices = null;
    @com.google.gson.annotations.SerializedName(value = "usage")
    @org.jetbrains.annotations.Nullable()
    private final com.example.abonekaptanmobile.data.remote.model.GroqUsage usage = null;
    @com.google.gson.annotations.SerializedName(value = "error")
    @org.jetbrains.annotations.Nullable()
    private final com.example.abonekaptanmobile.data.remote.model.GroqError error = null;
    
    public GroqChatResponse(@org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.Nullable()
    java.lang.String objectType, @org.jetbrains.annotations.Nullable()
    java.lang.Long created, @org.jetbrains.annotations.Nullable()
    java.lang.String model, @org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.GroqChoice> choices, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.GroqUsage usage, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.GroqError error) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getObjectType() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long getCreated() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getModel() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.GroqChoice> getChoices() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.GroqUsage getUsage() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.GroqError getError() {
        return null;
    }
    
    public GroqChatResponse() {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Long component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.GroqChoice> component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.GroqUsage component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.GroqError component7() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.GroqChatResponse copy(@org.jetbrains.annotations.Nullable()
    java.lang.String id, @org.jetbrains.annotations.Nullable()
    java.lang.String objectType, @org.jetbrains.annotations.Nullable()
    java.lang.Long created, @org.jetbrains.annotations.Nullable()
    java.lang.String model, @org.jetbrains.annotations.Nullable()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.GroqChoice> choices, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.GroqUsage usage, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.GroqError error) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}