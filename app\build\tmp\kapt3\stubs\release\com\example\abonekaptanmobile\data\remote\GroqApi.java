package com.example.abonekaptanmobile.data.remote;

import com.example.abonekaptanmobile.data.remote.model.GroqChatRequest;
import com.example.abonekaptanmobile.data.remote.model.GroqChatResponse;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * Turkish: Groq API için Retrofit arayüzü.
 * English: Retrofit interface for Groq API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J,\u0010\u0002\u001a\u00020\u00032\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0003\u0010\u0006\u001a\u00020\u00052\b\b\u0001\u0010\u0007\u001a\u00020\bH\u00a7@\u00a2\u0006\u0002\u0010\t\u00a8\u0006\n"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/GroqApi;", "", "chatCompletion", "Lcom/example/abonekaptanmobile/data/remote/model/GroqChatResponse;", "authToken", "", "contentType", "request", "Lcom/example/abonekaptanmobile/data/remote/model/GroqChatRequest;", "(Ljava/lang/String;Ljava/lang/String;Lcom/example/abonekaptanmobile/data/remote/model/GroqChatRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface GroqApi {
    
    /**
     * Turkish: Groq API'sine chat completion isteği gönderir.
     * English: Sends a chat completion request to Groq API.
     */
    @retrofit2.http.POST(value = "openai/v1/chat/completions")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object chatCompletion(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authToken, @retrofit2.http.Header(value = "Content-Type")
    @org.jetbrains.annotations.NotNull()
    java.lang.String contentType, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.model.GroqChatRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.remote.model.GroqChatResponse> $completion);
    
    /**
     * Turkish: Groq API için Retrofit arayüzü.
     * English: Retrofit interface for Groq API.
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}