# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.21"
  }
  digests {
    sha256: ";G\223\023\253l\256\244\345\342]=\356\214\250\f0,\211\272s\341\257M\257\252\020\017n\371)j"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.9.10"
  }
  digests {
    sha256: "\244\307M\224\326L\341\253\3457`\376\003\211\335\224\037o\305X\320\332\263^G\300\205\241\036\310\017("
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.9.10"
  }
  digests {
    sha256: "\254ca\277\232\321\3558,!\003\331q,G\315\354\026b2\264\220>\325\226\350\207k\006\201\311\267"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.21"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.compose"
    artifactId: "compose-bom"
    version: "2024.02.01"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-core-android"
    version: "1.6.2"
  }
  digests {
    sha256: "$@\243\257\331kz\362\231e\362=\304\227\204C`\272\022X\300\2369\314\343\242X\024\n2\t\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\234\324}V4\300\204\227\222\001Q\326\207\224-\021\344\376*6RU\360\211a-\034\377rL\266z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.2"
  }
  digests {
    sha256: "\\x(=\031V\261K\"\317j\327\fq*s\252\bA\026\267SU&\027l;\205\346\251 Z"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.2"
  }
  digests {
    sha256: "\354\301\031&%0\246\342\371\363s ~G-Gc\006\276\323\324\262\026\376a\261\352B\343\276\366\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.7.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.7.0"
  }
  digests {
    sha256: "\343k\216K\203\223\244\255\307N=J\262*\325\243c\226\360\316\242\344\vW4\352\341I7\337\322$"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\317{rd|y\225\a\025\210\376\207\004P\377\234\217\022\177%=-HQ\341a\270\000\366z\340"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.12.0"
  }
  digests {
    sha256: "B\377\247\312G\327\272\217\341\330t\305~\371\307\021\033\304\032+\f\f!Q\2129\340}\"-\355\213"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.0"
  }
  digests {
    sha256: "\306\353~g`\021\354e\263$(7=E\r\353\337\304Qy\304\370\263\247R\027O\270|\027\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.7.0"
  }
  digests {
    sha256: "\201\306\373\035\273j<\327\334\202}{\b\341\310\024.\323\244\000\243B$A\020\204\200\342/\2117\304"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.7.0"
  }
  digests {
    sha256: "S=\355\216\340dZ\030\366e=\245?\341\354Z\025C\016\1776\2477\324^A\0051\363\0173\030"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.1"
  }
  digests {
    sha256: "\020s\023v\f\030\370\332\027N\215\201\003PJF\216\200n\210\367\265Z\204\275\034\016\256\352\021\216\232"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.1"
  }
  digests {
    sha256: "t\226\317\375\323\353\020\020\232\315\332\0342\022\366\254x\025x\236\t8\r\311\342\314\336\304\226\333\243\374"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.1"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common-java8"
    version: "2.7.0"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.7.0"
  }
  digests {
    sha256: "\232\377\242La`\334\214\255\252\311B-OqNP\tS}\002C\257\304\274t\265\267\317\r\344\255"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.7.0"
  }
  digests {
    sha256: "\257\216\021\270~\003\rn1\a\3559\255\317\001\372\020\326%\236\261\\C\313\246\347\264\343\377\256\337\'"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "\324\253\314\350#V\203\2407M\324\351\247\217\324\026\353e\205VX\335\251\324\376K4\345\242\177\361g"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\003c\374\363\037\024z}\a\222X\0307_\304r\020gnB\214Q\321h\326\273\264\304\371\233\016\032"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.runtime"
    artifactId: "runtime-saveable-android"
    version: "1.6.2"
  }
  digests {
    sha256: ",2\252Hd\363s\231k\3005\350\357\316{3u\221\264\264,Kj\000yQ\237\000\376H\177\346"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\357p3\261]\262uiw\034<\n\353o\2229+VT!a\225\376t\023n\243\341\b\221*\324"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-compose"
    version: "2.7.0"
  }
  digests {
    sha256: "v@;\261Y\237n*\334\233\2007.\373&t\270\313\355\342\260\361\346\350\\J\224\341\f{\224\260"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\264\222\333\374\205\222dq6q\022\366\320\345\322\311\231\036\205\\T!\25379\223\226\314\001#\342\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.7.0"
  }
  digests {
    sha256: "N\035\222\342\211\222\f\327\265\0166q\271\031\033\324\023@sI\315\246\366\002\260(Td\364\034\034\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.7.0"
  }
  digests {
    sha256: "W\305x\206.!\2366\fiW\377\v\312o\026\227\a\261\345X-O\245\223\342\204\367\366>3\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.12.0"
  }
  digests {
    sha256: "\225\263\355\257x\"{|\310\330\002\321\037\207\223\251\256\322\222\345+\217\277\214\b\326\023L\313\027\026\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-service"
    version: "2.7.0"
  }
  digests {
    sha256: "\343\033\320\351+\320\263\0336\004H\312Z[\200\374\037cP{\264\356\216\233\323\000CtI\321x\233"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.7.0"
  }
  digests {
    sha256: "\251\177L!\221\353\3352\371\232\302)Y{\321\251$F\260\034\321\240\210\3214\224\314\005\312\277\r\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.7.0"
  }
  digests {
    sha256: "o\263=\224s\244\223=\251\331\212\v\022\266\006\022~\200h\033\331\271\003\t\314\322\302#\bc\2719"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.3.0"
  }
  digests {
    sha256: "4\350\262\277\307N#\301R^=\251\003\256D\233\177\033D\n\357E\341\201Y\356G\016\221\231\177H"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-compose"
    version: "1.8.2"
  }
  digests {
    sha256: "Zg\351\204\361N\322\257\305\205\252:#\355\377\034\027\221\310\f\252+\366\212\017y\234\033\021\243\2208"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.autofill"
    artifactId: "autofill"
    version: "1.0.0"
  }
  digests {
    sha256: "\311F\217V\340P\006\352\025\032BlT\225|\320y\233\213\203\245y\322\204m\322 a\363>^\315"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-geometry-android"
    version: "1.6.2"
  }
  digests {
    sha256: "t\355#\322?\344,\307ZV\031W\\\030\215\333\345R\'-\203\b\305\212\206Knu\330\006;\333"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-util-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\005\3530\3157L\337\221\246\233*\032\265\241\356A\312\351\362Wi\030\255\305\340\342\017lB\364\024."
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-graphics-android"
    version: "1.6.2"
  }
  digests {
    sha256: "Z+\225\206\025\222\351\366\230\327\316\234j\313\036\312\000\204\001\200/uV~\375`\233b\210@\227\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-unit-android"
    version: "1.6.2"
  }
  digests {
    sha256: "B\2243\177\256\\\236\260kuC\220\223\364\323\232<JN?p\214\025\356%5y\n\a\rZd"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-text-android"
    version: "1.6.2"
  }
  digests {
    sha256: "9\214\301\337|X>H\272\3462\271Es\305\t\2414\250\030J\361\377N{\302\344W\341?\345\023"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.ui"
    artifactId: "ui-tooling-preview-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\356\004\0276\366\203\n\307{\035\355\b\a\035g\233E0c%\366\335LT&\273\3521\375\334\243\306"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview-poolingcontainer"
    version: "1.0.0"
  }
  digests {
    sha256: "5\204\020/\304\233\363\231\305n;{\344\277\341 \000\304a\0222\f\330\317\205\314\n\217\223\363\347R"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\030_k,\305\247w\334#\246\026O\\\352\b\317\233\220\211\3507T\"dA\253e\264\213\031\364\334"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-android"
    version: "1.6.2"
  }
  digests {
    sha256: "mb\016\223\177*\244\"\325\241\205\225\323\233\000\035\221I\366\374\n\371}{\355\033\034M\323\242\312\342"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.animation"
    artifactId: "animation-core-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\322f\355\223\336\206\210]-S\001xh\331\320\342\2228f=\017Y\236\361\360\224x:\017\313\315&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.foundation"
    artifactId: "foundation-layout-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\301\177\227\354\372]\366\002\016[;n\211\305\266\027\340\352f\314\"\356\232\202C\003\372Bj{\305+"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-icons-extended-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\302\354\311\300\317}\364\221X.ikJ\356\371dS\333|\334\312X\342\310\341\026\262\223\2679\263A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple"
    version: "1.6.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material"
    artifactId: "material-ripple-android"
    version: "1.6.2"
  }
  digests {
    sha256: "\277y\356F\233T\375\360zYB\006\211W\221\243\322\376\257\310\320\321\331hH+L\221z\356~N"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3"
    version: "1.2.0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.compose.material3"
    artifactId: "material3-android"
    version: "1.2.0"
  }
  digests {
    sha256: "\020\372\315\350\366o\221\326\032H\310\337\354\343\264\223\3260\202/1\034\322\006C\2424\026\344\361\352\f"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "logging-interceptor"
    version: "4.12.0"
  }
  digests {
    sha256: "\363\350\325\360\220<%\f+U\322\364\177\317\340\b\350\00648]\2508Qa\307\246:\256\320\307L"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okhttp3"
    artifactId: "okhttp"
    version: "4.12.0"
  }
  digests {
    sha256: "\261\005\000\201\261K\267\243\247\345ZM>\360\033]\317\253\304S\264W:O\300\031vq\221\325\364\340"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio"
    version: "3.6.0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.okio"
    artifactId: "okio-jvm"
    version: "3.6.0"
  }
  digests {
    sha256: "gT?\a6\374B*\351\'\355\016PK\230\274^&\237\332\r5\000W\2237\313q=\242\204\022"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.0.0"
  }
  digests {
    sha256: "\002\271\240\234N\f\261z\r\211<\277@\3741\323p\2262a\233\v\375%\036\\Q\343\347\256\327\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.7"
  }
  digests {
    sha256: "Y\307A\020\271\223x\210^\320bB\370\242m\243\005\352M\312S\355`\0265@\335\016.<fu"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.0.0"
  }
  digests {
    sha256: " \345\270\366Rj4YZ`OVq\215\250\021g\300\264\nz\224\245}\2525Vc\362YM\362"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.0.1"
  }
  digests {
    sha256: "(\226\327oC+\345!g)[\271\316E\255\342\\1\n\357\374\004\322\214\370\333j\025\206\216\203\336"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.2.0"
  }
  digests {
    sha256: "\357C\353\374d\035qH\025CROF\321&y;L\265{\364f\304\337L\344=\f\265\341\033\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.0.1"
  }
  digests {
    sha256: "\361\006\333H\306\314\372\216\023\025\247\255\304J\354\320/\3675^\263\372}\315\313\250\302\203\250\353\026\201"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.0.1"
  }
  digests {
    sha256: "\263Ro\f\256\332\251lRl\304\311\272\224\262)\332\325\257\343\0372\364ol\326h\2406$\375B"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "retrofit"
    version: "2.9.0"
  }
  digests {
    sha256: "\346\352\031)\304hR\365\276\306j\2635}\243\203Gl\357N\215\035\356\375\277\031[y\314Me\201"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.squareup.retrofit2"
    artifactId: "converter-gson"
    version: "2.9.0"
  }
  digests {
    sha256: "2\252 k\232)\311\337^\332\223\240\222\317\263\260\271\023>#,\006+\252\210/\003\031\360\347\237\016"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.10.1"
  }
  digests {
    sha256: "BA\301Jw\'\303O\356\246P~\310\0011\212=J\220\360p\344RV\201\a\237\271N\344\305\223"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.api-client"
    artifactId: "google-api-client-android"
    version: "2.2.0"
  }
  digests {
    sha256: "\335\027\311\236\037][Q\313H\222\224SkP}\367\354\304\321\373\234\177\332\343Mb\206\374U\f\322"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.http-client"
    artifactId: "google-http-client"
    version: "1.43.3"
  }
  digests {
    sha256: "`\254\247B\214Z\037\363e[pT\032\230\377=p\335\355H\254\023$\332\341\2579\361\266\031\024\257"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.httpcomponents"
    artifactId: "httpclient"
    version: "4.5.14"
  }
  digests {
    sha256: "\310\274~\034Q\246\324\316r\364\r.\273\253\361\304\266\213\376v\3472\020K\0048\033I4x\351\326"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.apache.httpcomponents"
    artifactId: "httpcore"
    version: "4.4.16"
  }
  digests {
    sha256: "l\233=\321B\240\235\304h\342:\323\232\255ou\240\362\270Q%\020Di\360&\345*GNFO"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-logging"
    artifactId: "commons-logging"
    version: "1.2"
  }
  digests {
    sha256: "\332\335\352\036\240\276\017V\227\212\263\000k\212\311(4\257\356\373\331\267\344\3461o\312W\337\017\2466"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "commons-codec"
    artifactId: "commons-codec"
    version: "1.11"
  }
  digests {
    sha256: "\345\231\3251\216\227\252H\364!6\242\222~m\372N\210\201\337\360\346\310\343\020\235\333\277\365\035{}"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.18.0"
  }
  digests {
    sha256: "\236h\024\313q\201i\210\244\375\033\a\251\223\250\362\033\267\005\215R,\026+\035\350I\341\233\352T\256"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "32.0.0-android"
  }
  digests {
    sha256: "\226\225+9A3\252 \255?\030\364\373\tX+\022\250\252\225\347\214@ <\177\271\357\355\314/|"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.33.0"
  }
  digests {
    sha256: "\343\026%[\277\315\237\345\r\026S\024\270Z\273+3\313*f\251<I\035\266H\344\230\250,-\341"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "2.8"
  }
  digests {
    sha256: "\360*\225\372\032^\225\355\263\355\205\237\320\373}\367\t\321!\243R\220\357\370\267M\316*\267\364\326\355"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.opencensus"
    artifactId: "opencensus-api"
    version: "0.31.1"
  }
  digests {
    sha256: "\361GMG\364\266\260\001U\212\322{\225.5\355\245\314qFx\210w\374R\223\214n\272$\263\202"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.grpc"
    artifactId: "grpc-context"
    version: "1.27.2"
  }
  digests {
    sha256: "\274\277\220U\337\364S\375e\b\275|\312*\n\242\325\360Y\251\311K\356\321\365\375\241\334\001V\a\270"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "io.opencensus"
    artifactId: "opencensus-contrib-http-util"
    version: "0.31.1"
  }
  digests {
    sha256: ">\251\225\265Z@h\276\"\230\233p\314)\244\327\210\302\323(\321\325\006\023\247\251\257\321?\335-\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.apis"
    artifactId: "google-api-services-gmail"
    version: "v1-rev20220404-2.0.0"
  }
  digests {
    sha256: "\334tP\366_\201\240\351\3046\236^\tn\370\352\342\330\232l\026\316E\307\253K\324\334a\020B\004"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.api-client"
    artifactId: "google-api-client"
    version: "2.0.0"
  }
  digests {
    sha256: "\372\360/@p\304\365\221\347\321\245\221\026\035\243\276\342\320\216+\271\304W\206EU\236\217U\317k{"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.oauth-client"
    artifactId: "google-oauth-client"
    version: "1.34.1"
  }
  digests {
    sha256: "\031>\337\227\256\372(\271<X\222\275\305\230\272\303O\244\303\226X\2000\bO)\v\024@\350\271\212"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.http-client"
    artifactId: "google-http-client-gson"
    version: "1.43.3"
  }
  digests {
    sha256: "\343\032N\334\271\3109T\242X~\024\372/?\217J\255V\025#\201\2632\032;\320\274\256\003\372&"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.http-client"
    artifactId: "google-http-client-apache-v2"
    version: "1.42.1"
  }
  digests {
    sha256: "\213IUv\317\225\232\322o\317\335\003\315\367\202\372!t\022\fH\305\025\376q\267\001H\251Q\302\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.auth"
    artifactId: "google-auth-library-oauth2-http"
    version: "1.23.0"
  }
  digests {
    sha256: "\362\277s\225\t\265\363i|\261\2773\377\235\302~\217\310\206\316\333/cv\244X&?y>\3213"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.auto.value"
    artifactId: "auto-value-annotations"
    version: "1.10.4"
  }
  digests {
    sha256: "\341\304^k\352\332\357\227\227\313\r\232\375ZEb\032\320a\315\2062\001/\205X(S\243\210x%"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.auth"
    artifactId: "google-auth-library-credentials"
    version: "1.23.0"
  }
  digests {
    sha256: "\331\202\355\242\b5\343\001\334\276\354M\b2\211\244O\335\006\351\243\\\341\204I\005OO\375?\t\237"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-runtime"
    version: "2.6.1"
  }
  digests {
    sha256: "ibO\327\255\326\316[\374\301#b\315BsA\322\221\016\'~\325\246\374\304a2\244\211\221\024\320"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-common"
    version: "2.6.1"
  }
  digests {
    sha256: "\2312m>\354\244\246Fq\274\210\002\361\344_Nk4\216\214\177\253\2420\303\205M1h\t\377\317"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.room"
    artifactId: "room-ktx"
    version: "2.6.1"
  }
  digests {
    sha256: "\332L\f\272~\374\257\242\237\276\253\035\264\031\204#\217%\301\2436\022\301\326\rc\271\225\226\215p\312"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite"
    version: "2.4.0"
  }
  digests {
    sha256: "\273\177\241\023\021/~HWIn\".0Q\327:\221\n\335t\277@v\036\033\332\345[\002\026\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.sqlite"
    artifactId: "sqlite-framework"
    version: "2.4.0"
  }
  digests {
    sha256: "\312nP3\"\262\346\003t\302\263l\225\305\v\026p\235\223\210\3766\350\017\262=\341\373\367\246\353\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime-ktx"
    version: "2.9.0"
  }
  digests {
    sha256: "</\232\nQ\202o\033\247Gu\035\211\235\230W\336\371\227l\t\370\352\220\006k6J\274\262J\031"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.work"
    artifactId: "work-runtime"
    version: "2.9.0"
  }
  digests {
    sha256: "\213\205\363\212\250&\331\002\350\250\215*\371\334s\245\364?f\030r\004\205\361nt&\222\305\241\217o"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-android"
    version: "2.49"
  }
  digests {
    sha256: "\325\354}\236Af\320\256\342\245\241\324\354\372\2635(A\353p\347k\226\346\351\nKU8\334\355\365"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger"
    version: "2.49"
  }
  digests {
    sha256: "m\301\231\222\222\355\271\314\203v\t\364\213\342\213\370\031\223\326\264J\324O\375\276\210\230\344.\346U0"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "dagger-lint-aar"
    version: "2.49"
  }
  digests {
    sha256: "+#c\025\313\204\022\202w\350b\253\340\277\253\245\335\276M\243\177.\201^\005\252qs\250$\326\357"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.dagger"
    artifactId: "hilt-core"
    version: "2.49"
  }
  digests {
    sha256: "\033\225\266]d\267p\271J>\250\b\203\256\005\357\n\201Ns\213\fpu)b\242\216\365H\377!"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation-compose"
    version: "1.1.0"
  }
  digests {
    sha256: "\"1\324\\\331|\r\354\344L\270Z\270.r\336\275\277\366\277\244)I>\214td\235\022\031/E"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-navigation"
    version: "1.1.0"
  }
  digests {
    sha256: "F\241\321$\030\367zuO\311\034\\(\254\277\004\034x\366N\240\203\361\372\2234\235\216C\366\352\211"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.5.1"
  }
  digests {
    sha256: ">\201q\271n>\337\v<G\226\322\200\356\224\277>\275d\256\354\a\022;\025\214yd\313\224\\\034"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.5.1"
  }
  digests {
    sha256: "i`\250\340\236\v/\314_\177\r\0322\231\3718\372\255\030\253FVP`8\342u\'\320N\260\212"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-compose"
    version: "2.5.1"
  }
  digests {
    sha256: "h\316\246\321\215\036\361\267\313\374\3666\344\267\203\327l\205~\263\260tc\226\256\260\340\326x\311\v0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime-ktx"
    version: "2.5.1"
  }
  digests {
    sha256: "\347\035l\237\250\036$J\315U\337~>\026\254N\241Z\354\257\323\016\200\346E\327/_\357\3715\221"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common-ktx"
    version: "2.5.1"
  }
  digests {
    sha256: "\367F\376L\270\356#p\322\276\177P23\337\321\224\217_\355\376{\347\306\305\f7:\357\337\033Q"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-work"
    version: "1.1.0"
  }
  digests {
    sha256: "G\326\267\227==D\241COP\tQ\364q\363q\2612\316\242\244G\232\177RO\3261\343ry"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.hilt"
    artifactId: "hilt-common"
    version: "1.1.0"
  }
  digests {
    sha256: "k\t\b\037\325\241\r\345i\264\276dc\261F\366\004[\261\211\031\2770\232\377\006I\200\2661\003q"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 4
  library_dep_index: 3
}
library_dependencies {
  library_index: 2
  library_dep_index: 0
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 0
}
library_dependencies {
  library_index: 4
  library_dep_index: 0
}
library_dependencies {
  library_index: 5
  library_dep_index: 6
  library_dep_index: 77
  library_dep_index: 81
  library_dep_index: 8
  library_dep_index: 59
  library_dep_index: 66
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 7
  library_dep_index: 78
  library_dep_index: 82
  library_dep_index: 9
  library_dep_index: 60
  library_dep_index: 67
  library_dep_index: 35
  library_dep_index: 37
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 73
  library_dep_index: 69
  library_dep_index: 79
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 55
  library_dep_index: 61
  library_dep_index: 72
  library_dep_index: 76
  library_dep_index: 80
  library_dep_index: 74
  library_dep_index: 70
  library_dep_index: 64
  library_dep_index: 58
  library_dep_index: 56
  library_dep_index: 62
}
library_dependencies {
  library_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 8
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 77
  library_dep_index: 79
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
}
library_dependencies {
  library_index: 9
  library_dep_index: 10
  library_dep_index: 12
  library_dep_index: 54
  library_dep_index: 14
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 17
  library_dep_index: 68
  library_dep_index: 65
  library_dep_index: 22
  library_dep_index: 41
  library_dep_index: 51
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 69
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
  library_dep_index: 43
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 45
  library_dep_index: 0
  library_dep_index: 11
  library_dep_index: 53
}
library_dependencies {
  library_index: 11
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 17
  library_dep_index: 22
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 51
  library_dep_index: 44
  library_dep_index: 50
  library_dep_index: 0
  library_dep_index: 53
  library_dep_index: 10
}
library_dependencies {
  library_index: 12
  library_dep_index: 13
}
library_dependencies {
  library_index: 13
  library_dep_index: 0
}
library_dependencies {
  library_index: 14
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 16
  library_dep_index: 16
}
library_dependencies {
  library_index: 16
  library_dep_index: 14
  library_dep_index: 14
}
library_dependencies {
  library_index: 17
  library_dep_index: 12
  library_dep_index: 18
  library_dep_index: 14
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 22
  library_dep_index: 52
  library_dep_index: 0
  library_dep_index: 43
}
library_dependencies {
  library_index: 18
  library_dep_index: 0
}
library_dependencies {
  library_index: 19
  library_dep_index: 12
  library_dep_index: 20
}
library_dependencies {
  library_index: 21
  library_dep_index: 12
}
library_dependencies {
  library_index: 22
  library_dep_index: 12
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 51
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 23
  library_dep_index: 12
}
library_dependencies {
  library_index: 24
  library_dep_index: 12
  library_dep_index: 23
}
library_dependencies {
  library_index: 25
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 26
  library_dep_index: 27
  library_dep_index: 29
  library_dep_index: 2
}
library_dependencies {
  library_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 28
  library_dep_index: 1
  library_dep_index: 29
  library_dep_index: 4
  library_dep_index: 2
}
library_dependencies {
  library_index: 29
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 28
}
library_dependencies {
  library_index: 30
  library_dep_index: 12
  library_dep_index: 25
  library_dep_index: 25
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 31
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 32
  library_dep_index: 47
  library_dep_index: 0
  library_dep_index: 27
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 47
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 48
}
library_dependencies {
  library_index: 32
  library_dep_index: 23
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 33
  library_dep_index: 18
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 38
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 22
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 14
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 26
  library_dep_index: 27
  library_dep_index: 36
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 12
  library_dep_index: 34
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 34
}
library_dependencies {
  library_index: 38
  library_dep_index: 12
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 41
  library_dep_index: 25
  library_dep_index: 42
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 39
  library_dep_index: 18
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 40
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 42
  library_dep_index: 30
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 41
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 32
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 42
  library_dep_index: 12
  library_dep_index: 43
  library_dep_index: 32
  library_dep_index: 41
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 47
  library_dep_index: 48
}
library_dependencies {
  library_index: 43
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 0
  library_dep_index: 17
}
library_dependencies {
  library_index: 44
  library_dep_index: 12
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 0
  library_dep_index: 45
}
library_dependencies {
  library_index: 45
  library_dep_index: 44
  library_dep_index: 0
  library_dep_index: 44
}
library_dependencies {
  library_index: 46
  library_dep_index: 22
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 47
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 48
}
library_dependencies {
  library_index: 47
  library_dep_index: 32
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 48
}
library_dependencies {
  library_index: 48
  library_dep_index: 12
  library_dep_index: 22
  library_dep_index: 49
  library_dep_index: 0
  library_dep_index: 25
  library_dep_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 47
  library_dep_index: 22
  library_dep_index: 33
  library_dep_index: 38
  library_dep_index: 46
  library_dep_index: 41
  library_dep_index: 39
  library_dep_index: 40
  library_dep_index: 42
}
library_dependencies {
  library_index: 49
  library_dep_index: 12
  library_dep_index: 50
}
library_dependencies {
  library_index: 50
  library_dep_index: 12
}
library_dependencies {
  library_index: 51
  library_dep_index: 12
  library_dep_index: 19
  library_dep_index: 49
  library_dep_index: 20
}
library_dependencies {
  library_index: 52
  library_dep_index: 12
  library_dep_index: 14
}
library_dependencies {
  library_index: 53
  library_dep_index: 10
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 8
  library_dep_index: 41
  library_dep_index: 0
  library_dep_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 54
  library_dep_index: 17
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 12
  library_dep_index: 34
  library_dep_index: 57
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 61
  library_dep_index: 57
}
library_dependencies {
  library_index: 57
  library_dep_index: 58
}
library_dependencies {
  library_index: 58
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 61
}
library_dependencies {
  library_index: 59
  library_dep_index: 60
}
library_dependencies {
  library_index: 60
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 61
  library_dep_index: 57
}
library_dependencies {
  library_index: 61
  library_dep_index: 62
}
library_dependencies {
  library_index: 62
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 34
  library_dep_index: 55
  library_dep_index: 57
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 66
  library_dep_index: 57
}
library_dependencies {
  library_index: 63
  library_dep_index: 64
}
library_dependencies {
  library_index: 64
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 59
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 17
  library_dep_index: 65
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 27
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 66
  library_dep_index: 61
  library_dep_index: 57
}
library_dependencies {
  library_index: 65
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 17
  library_dep_index: 48
  library_dep_index: 49
}
library_dependencies {
  library_index: 66
  library_dep_index: 67
}
library_dependencies {
  library_index: 67
  library_dep_index: 12
  library_dep_index: 34
  library_dep_index: 4
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 61
  library_dep_index: 57
}
library_dependencies {
  library_index: 68
  library_dep_index: 43
  library_dep_index: 0
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
}
library_dependencies {
  library_index: 70
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 17
  library_dep_index: 65
  library_dep_index: 4
  library_dep_index: 75
}
library_dependencies {
  library_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 72
  library_dep_index: 12
  library_dep_index: 73
  library_dep_index: 75
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 55
  library_dep_index: 57
  library_dep_index: 4
  library_dep_index: 73
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 74
  library_dep_index: 12
  library_dep_index: 14
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 57
  library_dep_index: 0
  library_dep_index: 4
  library_dep_index: 27
  library_dep_index: 71
}
library_dependencies {
  library_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 76
  library_dep_index: 12
  library_dep_index: 73
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 57
  library_dep_index: 17
  library_dep_index: 4
  library_dep_index: 69
}
library_dependencies {
  library_index: 77
  library_dep_index: 78
}
library_dependencies {
  library_index: 78
  library_dep_index: 6
  library_dep_index: 34
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 79
}
library_dependencies {
  library_index: 79
  library_dep_index: 80
}
library_dependencies {
  library_index: 80
  library_dep_index: 71
  library_dep_index: 69
  library_dep_index: 34
  library_dep_index: 57
  library_dep_index: 4
  library_dep_index: 6
  library_dep_index: 77
}
library_dependencies {
  library_index: 81
  library_dep_index: 82
}
library_dependencies {
  library_index: 82
  library_dep_index: 53
  library_dep_index: 12
  library_dep_index: 18
  library_dep_index: 14
  library_dep_index: 73
  library_dep_index: 69
  library_dep_index: 75
  library_dep_index: 6
  library_dep_index: 79
  library_dep_index: 34
  library_dep_index: 59
  library_dep_index: 63
  library_dep_index: 57
  library_dep_index: 30
  library_dep_index: 22
  library_dep_index: 41
  library_dep_index: 45
  library_dep_index: 4
}
library_dependencies {
  library_index: 83
  library_dep_index: 84
  library_dep_index: 2
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
  library_dep_index: 2
}
library_dependencies {
  library_index: 85
  library_dep_index: 86
}
library_dependencies {
  library_index: 86
  library_dep_index: 2
  library_dep_index: 4
}
library_dependencies {
  library_index: 87
  library_dep_index: 88
  library_dep_index: 89
  library_dep_index: 92
  library_dep_index: 96
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 97
  library_dep_index: 95
}
library_dependencies {
  library_index: 88
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 18
  library_dep_index: 14
  library_dep_index: 43
  library_dep_index: 32
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 89
  library_dep_index: 44
  library_dep_index: 90
  library_dep_index: 0
}
library_dependencies {
  library_index: 89
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 32
  library_dep_index: 41
  library_dep_index: 14
}
library_dependencies {
  library_index: 90
  library_dep_index: 12
  library_dep_index: 17
  library_dep_index: 91
}
library_dependencies {
  library_index: 91
  library_dep_index: 12
  library_dep_index: 17
}
library_dependencies {
  library_index: 92
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 93
  library_dep_index: 14
  library_dep_index: 17
  library_dep_index: 88
  library_dep_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 94
  library_dep_index: 14
  library_dep_index: 17
  library_dep_index: 88
}
library_dependencies {
  library_index: 95
  library_dep_index: 94
}
library_dependencies {
  library_index: 96
  library_dep_index: 14
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 97
  library_dep_index: 93
  library_dep_index: 94
  library_dep_index: 95
}
library_dependencies {
  library_index: 98
  library_dep_index: 84
}
library_dependencies {
  library_index: 99
  library_dep_index: 98
  library_dep_index: 100
}
library_dependencies {
  library_index: 101
  library_dep_index: 102
}
library_dependencies {
  library_index: 102
  library_dep_index: 103
  library_dep_index: 104
  library_dep_index: 107
  library_dep_index: 108
  library_dep_index: 109
  library_dep_index: 112
  library_dep_index: 113
  library_dep_index: 115
}
library_dependencies {
  library_index: 103
  library_dep_index: 104
  library_dep_index: 105
  library_dep_index: 106
}
library_dependencies {
  library_index: 109
  library_dep_index: 110
  library_dep_index: 20
  library_dep_index: 107
  library_dep_index: 111
  library_dep_index: 108
  library_dep_index: 112
}
library_dependencies {
  library_index: 113
  library_dep_index: 114
}
library_dependencies {
  library_index: 115
  library_dep_index: 113
  library_dep_index: 109
}
library_dependencies {
  library_index: 116
  library_dep_index: 117
}
library_dependencies {
  library_index: 117
  library_dep_index: 118
  library_dep_index: 119
  library_dep_index: 109
  library_dep_index: 120
  library_dep_index: 102
}
library_dependencies {
  library_index: 118
  library_dep_index: 102
  library_dep_index: 119
  library_dep_index: 109
}
library_dependencies {
  library_index: 119
  library_dep_index: 102
  library_dep_index: 100
}
library_dependencies {
  library_index: 120
  library_dep_index: 102
}
library_dependencies {
  library_index: 121
  library_dep_index: 122
  library_dep_index: 107
  library_dep_index: 123
  library_dep_index: 102
  library_dep_index: 119
  library_dep_index: 109
  library_dep_index: 108
}
library_dependencies {
  library_index: 124
  library_dep_index: 18
  library_dep_index: 24
  library_dep_index: 125
  library_dep_index: 127
  library_dep_index: 128
  library_dep_index: 125
  library_dep_index: 126
}
library_dependencies {
  library_index: 125
  library_dep_index: 12
  library_dep_index: 2
  library_dep_index: 126
  library_dep_index: 124
}
library_dependencies {
  library_index: 126
  library_dep_index: 125
  library_dep_index: 124
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 125
  library_dep_index: 124
}
library_dependencies {
  library_index: 127
  library_dep_index: 12
  library_dep_index: 0
  library_dep_index: 128
}
library_dependencies {
  library_index: 128
  library_dep_index: 12
  library_dep_index: 127
  library_dep_index: 0
  library_dep_index: 127
}
library_dependencies {
  library_index: 129
  library_dep_index: 130
  library_dep_index: 130
}
library_dependencies {
  library_index: 130
  library_dep_index: 18
  library_dep_index: 17
  library_dep_index: 31
  library_dep_index: 46
  library_dep_index: 126
  library_dep_index: 128
  library_dep_index: 49
  library_dep_index: 20
  library_dep_index: 0
  library_dep_index: 26
  library_dep_index: 129
}
library_dependencies {
  library_index: 131
  library_dep_index: 132
  library_dep_index: 134
  library_dep_index: 135
  library_dep_index: 107
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 18
  library_dep_index: 88
  library_dep_index: 25
  library_dep_index: 41
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 133
  library_dep_index: 0
}
library_dependencies {
  library_index: 132
  library_dep_index: 133
}
library_dependencies {
  library_index: 135
  library_dep_index: 132
  library_dep_index: 107
  library_dep_index: 133
}
library_dependencies {
  library_index: 136
  library_dep_index: 34
  library_dep_index: 8
  library_dep_index: 137
  library_dep_index: 39
  library_dep_index: 140
  library_dep_index: 0
}
library_dependencies {
  library_index: 137
  library_dep_index: 12
  library_dep_index: 138
  library_dep_index: 131
  library_dep_index: 0
}
library_dependencies {
  library_index: 138
  library_dep_index: 10
  library_dep_index: 18
  library_dep_index: 14
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 139
  library_dep_index: 0
}
library_dependencies {
  library_index: 139
  library_dep_index: 12
  library_dep_index: 16
  library_dep_index: 43
  library_dep_index: 38
  library_dep_index: 40
  library_dep_index: 42
  library_dep_index: 45
  library_dep_index: 0
}
library_dependencies {
  library_index: 140
  library_dep_index: 53
  library_dep_index: 71
  library_dep_index: 75
  library_dep_index: 34
  library_dep_index: 36
  library_dep_index: 8
  library_dep_index: 30
  library_dep_index: 39
  library_dep_index: 141
  library_dep_index: 0
}
library_dependencies {
  library_index: 141
  library_dep_index: 142
  library_dep_index: 138
}
library_dependencies {
  library_index: 142
  library_dep_index: 139
}
library_dependencies {
  library_index: 143
  library_dep_index: 12
  library_dep_index: 144
  library_dep_index: 130
  library_dep_index: 131
}
library_dependencies {
  library_index: 144
  library_dep_index: 135
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 5
  dependency_index: 43
  dependency_index: 38
  dependency_index: 53
  dependency_index: 83
  dependency_index: 8
  dependency_index: 59
  dependency_index: 66
  dependency_index: 81
  dependency_index: 6
  dependency_index: 77
  dependency_index: 87
  dependency_index: 98
  dependency_index: 99
  dependency_index: 100
  dependency_index: 101
  dependency_index: 116
  dependency_index: 121
  dependency_index: 123
  dependency_index: 124
  dependency_index: 126
  dependency_index: 129
  dependency_index: 131
  dependency_index: 136
  dependency_index: 143
  dependency_index: 40
  dependency_index: 39
  dependency_index: 33
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
