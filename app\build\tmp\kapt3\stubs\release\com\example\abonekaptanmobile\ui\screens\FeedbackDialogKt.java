package com.example.abonekaptanmobile.ui.screens;

import androidx.compose.foundation.layout.*;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import com.example.abonekaptanmobile.R;
import com.example.abonekaptanmobile.model.SubscriptionItem;
import com.example.abonekaptanmobile.model.SubscriptionStatus;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000,\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u001a\u0082\u0001\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00010\u00052b\u0010\u0006\u001a^\u0012\u0013\u0012\u00110\b\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000b\u0012\u0013\u0012\u00110\f\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\r\u0012\u0013\u0012\u00110\b\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000e\u0012\u0015\u0012\u0013\u0018\u00010\b\u00a2\u0006\f\b\t\u0012\b\b\n\u0012\u0004\b\b(\u000f\u0012\u0004\u0012\u00020\u00010\u0007H\u0007\u00a8\u0006\u0010"}, d2 = {"FeedbackDialog", "", "item", "Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "onDismiss", "Lkotlin/Function0;", "onSubmit", "Lkotlin/Function4;", "", "Lkotlin/ParameterName;", "name", "serviceName", "Lcom/example/abonekaptanmobile/model/SubscriptionStatus;", "originalStatus", "feedbackLabel", "note", "app_release"})
public final class FeedbackDialogKt {
    
    @androidx.compose.runtime.Composable()
    public static final void FeedbackDialog(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionItem item, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onDismiss, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function4<? super java.lang.String, ? super com.example.abonekaptanmobile.model.SubscriptionStatus, ? super java.lang.String, ? super java.lang.String, kotlin.Unit> onSubmit) {
    }
}