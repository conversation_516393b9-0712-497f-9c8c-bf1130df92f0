// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;
import com.example.abonekaptanmobile.data.repository.SubscriptionRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideSubscriptionRepositoryFactory implements Factory<SubscriptionRepository> {
  private final Provider<SubscriptionDao> daoProvider;

  public AppModule_ProvideSubscriptionRepositoryFactory(Provider<SubscriptionDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public SubscriptionRepository get() {
    return provideSubscriptionRepository(daoProvider.get());
  }

  public static AppModule_ProvideSubscriptionRepositoryFactory create(
      Provider<SubscriptionDao> daoProvider) {
    return new AppModule_ProvideSubscriptionRepositoryFactory(daoProvider);
  }

  public static SubscriptionRepository provideSubscriptionRepository(SubscriptionDao dao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideSubscriptionRepository(dao));
  }
}
