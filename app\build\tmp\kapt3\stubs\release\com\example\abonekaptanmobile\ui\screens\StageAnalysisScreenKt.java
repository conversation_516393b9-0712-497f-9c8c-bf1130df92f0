package com.example.abonekaptanmobile.ui.screens;

import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.style.TextOverflow;
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity;
import com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel;
import java.text.SimpleDateFormat;
import java.util.*;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0000\u001a\u0016\u0010\u0000\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0003\u001a\u0010\u0010\u0005\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u0004H\u0003\u001a\u0010\u0010\u0007\u001a\u00020\u00012\u0006\u0010\b\u001a\u00020\tH\u0003\u001a\u0010\u0010\n\u001a\u00020\u00012\u0006\u0010\u000b\u001a\u00020\tH\u0003\u001a\u0010\u0010\f\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u0004H\u0003\u001a\u0016\u0010\r\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0003\u001a\u0010\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u0006\u001a\u00020\u0004H\u0003\u001a\u0016\u0010\u000f\u001a\u00020\u00012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0003\u001a\u0012\u0010\u0010\u001a\u00020\u00012\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001a*\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\t2\u0018\u0010\u0015\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\t\u0012\u0004\u0012\u00020\t0\u00160\u0003H\u0003\u001a \u0010\u0017\u001a\u00020\u00012\b\u0010\u0018\u001a\u0004\u0018\u00010\u00192\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u0003\u001a\u0010\u0010\u001a\u001a\u00020\t2\u0006\u0010\u001b\u001a\u00020\u001cH\u0002\u00a8\u0006\u001d"}, d2 = {"AllResultsTab", "", "results", "", "Lcom/example/abonekaptanmobile/data/local/entity/StageAnalysisEntity;", "AnalysisResultCard", "result", "EmailTypeChip", "type", "", "ProcessingStatusIcon", "status", "Stage1ResultCard", "Stage1Tab", "Stage2ResultCard", "Stage2Tab", "StageAnalysisScreen", "viewModel", "Lcom/example/abonekaptanmobile/ui/viewmodel/StageAnalysisViewModel;", "StatisticsCard", "title", "items", "Lkotlin/Pair;", "StatisticsTab", "statistics", "Lcom/example/abonekaptanmobile/data/local/dao/AnalysisStatistics;", "formatDate", "timestamp", "", "app_release"})
public final class StageAnalysisScreenKt {
    
    /**
     * Turkish: İki aşamalı analiz sonuçlarını gösteren ekran.
     * English: Screen showing two-stage analysis results.
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void StageAnalysisScreen(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AllResultsTab(java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> results) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void Stage1Tab(java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> results) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void Stage2Tab(java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> results) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatisticsTab(com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics statistics, java.util.List<com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity> results) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AnalysisResultCard(com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity result) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void Stage1ResultCard(com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity result) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void Stage2ResultCard(com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity result) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatisticsCard(java.lang.String title, java.util.List<kotlin.Pair<java.lang.String, java.lang.String>> items) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ProcessingStatusIcon(java.lang.String status) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmailTypeChip(java.lang.String type) {
    }
    
    private static final java.lang.String formatDate(long timestamp) {
        return null;
    }
}