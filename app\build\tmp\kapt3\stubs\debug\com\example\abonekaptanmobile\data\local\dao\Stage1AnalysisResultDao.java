package com.example.abonekaptanmobile.data.local.dao;

import androidx.room.*;
import com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult;
import kotlinx.coroutines.flow.Flow;

/**
 * Turkish: Aşama 1 analiz sonuçları için DAO
 * English: DAO for Stage 1 analysis results
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u000b\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\u000f0\u000eH\'J\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00130\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u001c\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f2\u0006\u0010\u0015\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0017J\u001c\u0010\u0018\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f2\u0006\u0010\u0019\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u001c\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000f2\u0006\u0010\u001d\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJ\u000e\u0010\u001e\u001a\u00020\u0016H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010 \u001a\u00020\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\"\u0010!\u001a\b\u0012\u0004\u0012\u00020\u00070\u000f2\f\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u000b0\u000fH\u00a7@\u00a2\u0006\u0002\u0010#J\u0016\u0010$\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\f\u00a8\u0006%"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/dao/Stage1AnalysisResultDao;", "", "deleteAllResults", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteOldResults", "timestamp", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteResult", "result", "Lcom/example/abonekaptanmobile/data/local/entity/Stage1AnalysisResult;", "(Lcom/example/abonekaptanmobile/data/local/entity/Stage1AnalysisResult;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllResults", "Lkotlinx/coroutines/flow/Flow;", "", "getLatestAnalysisTimestamp", "getNoResponses", "getResponseStatistics", "Lcom/example/abonekaptanmobile/data/local/dao/ResponseStatistic;", "getResultsByBatch", "batchNumber", "", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getResultsByDomain", "domain", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getResultsBySenderName", "senderName", "getTotalAnalyzedCount", "getYesResponses", "insertResult", "insertResults", "results", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateResult", "app_debug"})
@androidx.room.Dao()
public abstract interface Stage1AnalysisResultDao {
    
    /**
     * Turkish: Tüm Aşama 1 sonuçlarını getir
     * English: Get all Stage 1 results
     */
    @androidx.room.Query(value = "SELECT * FROM stage1_analysis_results ORDER BY analysisTimestamp DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult>> getAllResults();
    
    /**
     * Turkish: Belirli bir batch'in sonuçlarını getir
     * English: Get results for a specific batch
     */
    @androidx.room.Query(value = "SELECT * FROM stage1_analysis_results WHERE batchNumber = :batchNumber ORDER BY emailIndex")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getResultsByBatch(int batchNumber, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult>> $completion);
    
    /**
     * Turkish: EVET cevabı alan e-postaları getir
     * English: Get emails that received YES response
     */
    @androidx.room.Query(value = "SELECT * FROM stage1_analysis_results WHERE aiResponse = \'EVET\' ORDER BY analysisTimestamp DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getYesResponses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult>> $completion);
    
    /**
     * Turkish: HAYIR cevabı alan e-postaları getir
     * English: Get emails that received NO response
     */
    @androidx.room.Query(value = "SELECT * FROM stage1_analysis_results WHERE aiResponse = \'HAYIR\' ORDER BY analysisTimestamp DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getNoResponses(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult>> $completion);
    
    /**
     * Turkish: Belirli domain'e ait sonuçları getir
     * English: Get results for specific domain
     */
    @androidx.room.Query(value = "SELECT * FROM stage1_analysis_results WHERE domain LIKE \'%\' || :domain || \'%\' ORDER BY analysisTimestamp DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getResultsByDomain(@org.jetbrains.annotations.NotNull()
    java.lang.String domain, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult>> $completion);
    
    /**
     * Turkish: Belirli gönderen ismine ait sonuçları getir
     * English: Get results for specific sender name
     */
    @androidx.room.Query(value = "SELECT * FROM stage1_analysis_results WHERE senderName LIKE \'%\' || :senderName || \'%\' ORDER BY analysisTimestamp DESC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getResultsBySenderName(@org.jetbrains.annotations.NotNull()
    java.lang.String senderName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult>> $completion);
    
    /**
     * Turkish: En son analiz tarihini getir
     * English: Get latest analysis timestamp
     */
    @androidx.room.Query(value = "SELECT MAX(analysisTimestamp) FROM stage1_analysis_results")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getLatestAnalysisTimestamp(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * Turkish: Toplam analiz edilen e-posta sayısını getir
     * English: Get total analyzed email count
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM stage1_analysis_results")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalAnalyzedCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * Turkish: EVET/HAYIR istatistiklerini getir
     * English: Get YES/NO statistics
     */
    @androidx.room.Query(value = "\n        SELECT \n            aiResponse,\n            COUNT(*) as count\n        FROM stage1_analysis_results \n        GROUP BY aiResponse\n    ")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getResponseStatistics(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.dao.ResponseStatistic>> $completion);
    
    /**
     * Turkish: Sonuç ekle
     * English: Insert result
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertResult(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult result, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Long> $completion);
    
    /**
     * Turkish: Birden fazla sonuç ekle
     * English: Insert multiple results
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertResults(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult> results, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<java.lang.Long>> $completion);
    
    /**
     * Turkish: Sonuç güncelle
     * English: Update result
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateResult(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult result, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Sonuç sil
     * English: Delete result
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteResult(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult result, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tüm sonuçları sil
     * English: Delete all results
     */
    @androidx.room.Query(value = "DELETE FROM stage1_analysis_results")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllResults(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Belirli tarihten eski sonuçları sil
     * English: Delete results older than specific date
     */
    @androidx.room.Query(value = "DELETE FROM stage1_analysis_results WHERE analysisTimestamp < :timestamp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldResults(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}