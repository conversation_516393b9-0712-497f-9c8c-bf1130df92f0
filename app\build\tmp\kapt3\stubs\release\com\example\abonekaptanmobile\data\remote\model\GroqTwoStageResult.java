package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: <PERSON><PERSON> aşamalı analiz için Groq sonucu.
 * English: Groq result for two-stage analysis.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0007\n\u0002\b\u0010\n\u0002\u0010\b\n\u0002\b\u0003\b\u0086\b\u0018\u0000 \u001a2\u00020\u0001:\u0001\u001aB)\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0010\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0011\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0007H\u00c6\u0003J\u000b\u0010\u0013\u001a\u0004\u0018\u00010\u0005H\u00c6\u0003J3\u0010\u0014\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\u0005H\u00c6\u0001J\u0013\u0010\u0015\u001a\u00020\u00032\b\u0010\u0016\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0017\u001a\u00020\u0018H\u00d6\u0001J\t\u0010\u0019\u001a\u00020\u0005H\u00d6\u0001R\u0011\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u000eR\u0013\u0010\b\u001a\u0004\u0018\u00010\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\r\u00a8\u0006\u001b"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GroqTwoStageResult;", "", "isSubscriptionCompany", "", "companyName", "", "companyConfidence", "", "reasoning", "(ZLjava/lang/String;FLjava/lang/String;)V", "getCompanyConfidence", "()F", "getCompanyName", "()Ljava/lang/String;", "()Z", "getReasoning", "component1", "component2", "component3", "component4", "copy", "equals", "other", "hashCode", "", "toString", "Companion", "app_release"})
public final class GroqTwoStageResult {
    private final boolean isSubscriptionCompany = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String companyName = null;
    private final float companyConfidence = 0.0F;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String reasoning = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.data.remote.model.GroqTwoStageResult.Companion Companion = null;
    
    public GroqTwoStageResult(boolean isSubscriptionCompany, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, float companyConfidence, @org.jetbrains.annotations.Nullable()
    java.lang.String reasoning) {
        super();
    }
    
    public final boolean isSubscriptionCompany() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getCompanyName() {
        return null;
    }
    
    public final float getCompanyConfidence() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getReasoning() {
        return null;
    }
    
    public final boolean component1() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final float component3() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.GroqTwoStageResult copy(boolean isSubscriptionCompany, @org.jetbrains.annotations.NotNull()
    java.lang.String companyName, float companyConfidence, @org.jetbrains.annotations.Nullable()
    java.lang.String reasoning) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006J\u0010\u0010\u0007\u001a\u00020\u00042\u0006\u0010\b\u001a\u00020\tH\u0002J\u0010\u0010\n\u001a\u00020\u00042\u0006\u0010\u000b\u001a\u00020\tH\u0002\u00a8\u0006\f"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GroqTwoStageResult$Companion;", "", "()V", "fromGroqResponse", "Lcom/example/abonekaptanmobile/data/remote/model/GroqTwoStageResult;", "response", "Lcom/example/abonekaptanmobile/data/remote/model/GroqChatResponse;", "parseJsonStageResponse", "jsonStr", "", "parseTextStageResponse", "content", "app_release"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.abonekaptanmobile.data.remote.model.GroqTwoStageResult fromGroqResponse(@org.jetbrains.annotations.NotNull()
        com.example.abonekaptanmobile.data.remote.model.GroqChatResponse response) {
            return null;
        }
        
        private final com.example.abonekaptanmobile.data.remote.model.GroqTwoStageResult parseJsonStageResponse(java.lang.String jsonStr) {
            return null;
        }
        
        private final com.example.abonekaptanmobile.data.remote.model.GroqTwoStageResult parseTextStageResponse(java.lang.String content) {
            return null;
        }
    }
}