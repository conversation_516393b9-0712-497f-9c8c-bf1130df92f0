// Generated by Da<PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import com.example.abonekaptanmobile.data.repository.EmailRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideEmailRepositoryFactory implements Factory<EmailRepository> {
  private final Provider<EmailDao> daoProvider;

  public AppModule_ProvideEmailRepositoryFactory(Provider<EmailDao> daoProvider) {
    this.daoProvider = daoProvider;
  }

  @Override
  public EmailRepository get() {
    return provideEmailRepository(daoProvider.get());
  }

  public static AppModule_ProvideEmailRepositoryFactory create(Provider<EmailDao> daoProvider) {
    return new AppModule_ProvideEmailRepositoryFactory(daoProvider);
  }

  public static EmailRepository provideEmailRepository(EmailDao dao) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideEmailRepository(dao));
  }
}
