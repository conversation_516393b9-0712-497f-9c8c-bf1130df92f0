// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.remote.GroqApi;
import com.example.abonekaptanmobile.data.repository.GroqRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideGroqRepositoryFactory implements Factory<GroqRepository> {
  private final Provider<GroqApi> groqApiProvider;

  public AppModule_ProvideGroqRepositoryFactory(Provider<GroqApi> groqApiProvider) {
    this.groqApiProvider = groqApiProvider;
  }

  @Override
  public GroqRepository get() {
    return provideGroqRepository(groqApiProvider.get());
  }

  public static AppModule_ProvideGroqRepositoryFactory create(Provider<GroqApi> groqApiProvider) {
    return new AppModule_ProvideGroqRepositoryFactory(groqApiProvider);
  }

  public static GroqRepository provideGroqRepository(GroqApi groqApi) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideGroqRepository(groqApi));
  }
}
