package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.SubscriptionDao;
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity;
import com.example.abonekaptanmobile.model.SubscriptionItem;
import com.example.abonekaptanmobile.model.SubscriptionStatus;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * Turkish: Abonelik veritabanı işlemleri için repository.
 * English: Repository for subscription database operations.
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0007J\u0012\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tJ\u0012\u0010\f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tJ\u0012\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tJ\u000e\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0007J\u001c\u0010\u0010\u001a\u00020\u00062\f\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00120\nH\u0086@\u00a2\u0006\u0002\u0010\u0013J\u000e\u0010\u0014\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u000bR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/example/abonekaptanmobile/data/repository/SubscriptionRepository;", "", "subscriptionDao", "Lcom/example/abonekaptanmobile/data/local/dao/SubscriptionDao;", "(Lcom/example/abonekaptanmobile/data/local/dao/SubscriptionDao;)V", "deleteAllSubscriptions", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getActiveSubscriptions", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/abonekaptanmobile/data/local/entity/SubscriptionEntity;", "getAllSubscriptions", "getCancelledSubscriptions", "getSubscriptionCount", "", "insertSubscriptions", "subscriptions", "Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "mapToSubscriptionItem", "entity", "app_debug"})
public final class SubscriptionRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.local.dao.SubscriptionDao subscriptionDao = null;
    
    @javax.inject.Inject()
    public SubscriptionRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.SubscriptionDao subscriptionDao) {
        super();
    }
    
    /**
     * Turkish: Abonelikleri veritabanına kaydet.
     * English: Insert subscriptions into database.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertSubscriptions(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem> subscriptions, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: Tüm abonelikleri getir.
     * English: Get all subscriptions.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getAllSubscriptions() {
        return null;
    }
    
    /**
     * Turkish: Aktif abonelikleri getir.
     * English: Get active subscriptions.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getActiveSubscriptions() {
        return null;
    }
    
    /**
     * Turkish: İptal edilmiş abonelikleri getir.
     * English: Get cancelled subscriptions.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity>> getCancelledSubscriptions() {
        return null;
    }
    
    /**
     * Turkish: Abonelik sayısını getir.
     * English: Get subscription count.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSubscriptionCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * Turkish: Tüm abonelikleri sil.
     * English: Delete all subscriptions.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteAllSubscriptions(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: SubscriptionEntity'yi SubscriptionItem'a dönüştür.
     * English: Convert SubscriptionEntity to SubscriptionItem.
     */
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.model.SubscriptionItem mapToSubscriptionItem(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity entity) {
        return null;
    }
}