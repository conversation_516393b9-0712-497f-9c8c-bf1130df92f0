package com.example.abonekaptanmobile.utils

import android.content.Context
import android.os.Environment
import android.util.Log
import com.example.abonekaptanmobile.data.local.AppDatabase
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import javax.inject.Inject
import javax.inject.Singleton
import dagger.hilt.android.qualifiers.ApplicationContext

/**
 * Turkish: Veritabanını dışa aktarma utility'si.
 * English: Database export utility.
 */
@Singleton
class DatabaseExporter @Inject constructor(
    @ApplicationContext private val context: Context,
    private val database: AppDatabase
) {
    companion object {
        private const val TAG = "DatabaseExporter"
        private const val EXPORT_FILENAME = "abone_kaptan_export.sqlite"
    }

    /**
     * Turkish: Veritabanını external storage'a export et.
     * English: Export database to external storage.
     */
    suspend fun exportDatabase(): Result<String> = withContext(Dispatchers.IO) {
        try {
            // Veritabanı bağlantısını kapat
            database.close()
            
            // Kaynak veritabanı dosyası
            val sourceDbFile = context.getDatabasePath(AppDatabase.DATABASE_NAME)
            
            if (!sourceDbFile.exists()) {
                return@withContext Result.failure(Exception("Veritabanı dosyası bulunamadı"))
            }
            
            // Hedef dosya (Downloads klasörü)
            val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            if (!downloadsDir.exists()) {
                downloadsDir.mkdirs()
            }
            
            val targetFile = File(downloadsDir, EXPORT_FILENAME)
            
            // Dosyayı kopyala
            copyFile(sourceDbFile, targetFile)
            
            Log.d(TAG, "Veritabanı export edildi: ${targetFile.absolutePath}")
            Result.success(targetFile.absolutePath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Veritabanı export hatası: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * Turkish: Veritabanını /sdcard/ klasörüne export et (ADB erişimi için).
     * English: Export database to /sdcard/ folder (for ADB access).
     */
    suspend fun exportDatabaseToSdcard(): Result<String> = withContext(Dispatchers.IO) {
        try {
            // Veritabanı bağlantısını kapat
            database.close()
            
            // Kaynak veritabanı dosyası
            val sourceDbFile = context.getDatabasePath(AppDatabase.DATABASE_NAME)
            
            if (!sourceDbFile.exists()) {
                return@withContext Result.failure(Exception("Veritabanı dosyası bulunamadı"))
            }
            
            // Hedef dosya (/sdcard/)
            val targetFile = File("/sdcard/$EXPORT_FILENAME")
            
            // Dosyayı kopyala
            copyFile(sourceDbFile, targetFile)
            
            Log.d(TAG, "Veritabanı /sdcard/'a export edildi: ${targetFile.absolutePath}")
            Result.success(targetFile.absolutePath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Veritabanı /sdcard/ export hatası: ${e.message}", e)
            Result.failure(e)
        }
    }
    
    /**
     * Turkish: Dosya kopyalama işlemi.
     * English: File copy operation.
     */
    private fun copyFile(source: File, target: File) {
        try {
            FileInputStream(source).use { input ->
                FileOutputStream(target).use { output ->
                    val buffer = ByteArray(1024)
                    var length: Int
                    while (input.read(buffer).also { length = it } > 0) {
                        output.write(buffer, 0, length)
                    }
                    output.flush()
                }
            }
        } catch (e: IOException) {
            throw Exception("Dosya kopyalama hatası: ${e.message}", e)
        }
    }
    
    /**
     * Turkish: Export edilen dosyanın varlığını kontrol et.
     * English: Check if exported file exists.
     */
    fun checkExportedFile(): String? {
        val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        val exportFile = File(downloadsDir, EXPORT_FILENAME)
        
        return if (exportFile.exists()) {
            exportFile.absolutePath
        } else {
            null
        }
    }
    
    /**
     * Turkish: /sdcard/'daki export dosyasını kontrol et.
     * English: Check exported file in /sdcard/.
     */
    fun checkSdcardExportFile(): String? {
        val exportFile = File("/sdcard/$EXPORT_FILENAME")
        
        return if (exportFile.exists()) {
            exportFile.absolutePath
        } else {
            null
        }
    }
}
