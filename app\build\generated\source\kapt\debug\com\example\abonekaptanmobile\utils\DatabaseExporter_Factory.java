// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.utils;

import android.content.Context;
import com.example.abonekaptanmobile.data.local.AppDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DatabaseExporter_Factory implements Factory<DatabaseExporter> {
  private final Provider<Context> contextProvider;

  private final Provider<AppDatabase> databaseProvider;

  public DatabaseExporter_Factory(Provider<Context> contextProvider,
      Provider<AppDatabase> databaseProvider) {
    this.contextProvider = contextProvider;
    this.databaseProvider = databaseProvider;
  }

  @Override
  public DatabaseExporter get() {
    return newInstance(contextProvider.get(), databaseProvider.get());
  }

  public static DatabaseExporter_Factory create(Provider<Context> contextProvider,
      Provider<AppDatabase> databaseProvider) {
    return new DatabaseExporter_Factory(contextProvider, databaseProvider);
  }

  public static DatabaseExporter newInstance(Context context, AppDatabase database) {
    return new DatabaseExporter(context, database);
  }
}
