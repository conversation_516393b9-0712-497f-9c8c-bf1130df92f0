// file: app/java/com/example/abonekaptanmobile/data/remote/GroqApi.kt
package com.example.abonekaptanmobile.data.remote

import com.example.abonekaptanmobile.data.remote.model.GroqChatRequest
import com.example.abonekaptanmobile.data.remote.model.GroqChatResponse
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

/**
 * Turkish: Groq API için Retrofit arayüzü.
 * English: Retrofit interface for Groq API.
 */
interface GroqApi {

    /**
     * Turkish: Groq API'sine chat completion isteği gönderir.
     * English: Sends a chat completion request to Groq API.
     */
    @POST("openai/v1/chat/completions")
    suspend fun chatCompletion(
        @Header("Authorization") authToken: String,
        @Header("Content-Type") contentType: String = "application/json",
        @Body request: GroqChatRequest
    ): GroqChatResponse
}
