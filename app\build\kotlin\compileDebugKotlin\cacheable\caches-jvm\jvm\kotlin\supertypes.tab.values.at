/ Header Record For PersistentHashMapValueStorage= android.app.Application$androidx.work.Configuration.Provider$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.work.CoroutineWorker androidx.room.RoomDatabase androidx.lifecycle.ViewModel dagger.internal.FactoryE Dcom.example.abonekaptanmobile.data.local.dao.Stage1AnalysisResultDao dagger.internal.Factory5 4com.example.abonekaptanmobile.data.local.AppDatabase androidx.lifecycle.ViewModel dagger.internal.Factory