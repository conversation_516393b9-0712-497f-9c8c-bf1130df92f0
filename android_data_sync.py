#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Android Cihazından Gerçek Veri Senkronizasyonu
Real Data Sync from Android Device
"""

import subprocess
import sqlite3
import os
import time
from datetime import datetime
import shutil

class AndroidDataSync:
    """Android cihazından gerçek verileri çeken sınıf"""
    
    def __init__(self):
        self.device_id = "172.25.200.188:45883"  # Bağlı cihaz
        self.package_name = "com.example.abonekaptanmobile"
        self.db_name = "abone_kaptan_db"
        self.local_db = "abone_kaptan_db.sqlite"
        self.temp_db = "temp_android_db.sqlite"
        
    def check_device_connection(self):
        """Cihaz bağlantısını kontrol et"""
        try:
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            if self.device_id in result.stdout:
                print(f"✅ Cihaz bağlı: {self.device_id}")
                return True
            else:
                print(f"❌ Cihaz bulunamadı: {self.device_id}")
                return False
        except Exception as e:
            print(f"❌ ADB hatası: {e}")
            return False
    
    def check_app_installed(self):
        """Uygulamanın yüklü olup olmadığını kontrol et"""
        try:
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'pm', 'list', 'packages', self.package_name
            ], capture_output=True, text=True)
            
            if self.package_name in result.stdout:
                print(f"✅ Uygulama yüklü: {self.package_name}")
                return True
            else:
                print(f"❌ Uygulama yüklü değil: {self.package_name}")
                return False
        except Exception as e:
            print(f"❌ Uygulama kontrol hatası: {e}")
            return False
    
    def check_database_exists(self):
        """Veritabanının cihazda var olup olmadığını kontrol et"""
        try:
            db_path = f"/data/data/{self.package_name}/databases/{self.db_name}"
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'ls', db_path
            ], capture_output=True, text=True)
            
            if self.db_name in result.stdout:
                print(f"✅ Veritabanı bulundu: {db_path}")
                return True
            else:
                print(f"❌ Veritabanı bulunamadı: {db_path}")
                print("💡 Android uygulamasında email analizi yapın!")
                return False
        except Exception as e:
            print(f"❌ Veritabanı kontrol hatası: {e}")
            return False
    
    def pull_database(self):
        """Veritabanını cihazdan çek"""
        try:
            print("📱 Veritabanı cihazdan çekiliyor...")
            
            # Veritabanını /sdcard/'a kopyala
            db_path = f"/data/data/{self.package_name}/databases/{self.db_name}"
            sdcard_path = f"/sdcard/{self.db_name}"
            
            # Root ile kopyala
            copy_result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'su', '-c', 
                f'cp {db_path} {sdcard_path}'
            ], capture_output=True, text=True)
            
            if copy_result.returncode != 0:
                print("❌ Root erişimi gerekli veya veritabanı kopyalanamadı")
                print("💡 Cihazınızın root'lu olduğundan emin olun")
                return False
            
            # Bilgisayara çek
            pull_result = subprocess.run([
                'adb', '-s', self.device_id, 'pull', sdcard_path, self.temp_db
            ], capture_output=True, text=True)
            
            if pull_result.returncode != 0:
                print(f"❌ Veritabanı çekme hatası: {pull_result.stderr}")
                return False
            
            # Geçici dosyayı temizle
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'rm', sdcard_path
            ], capture_output=True, text=True)
            
            print("✅ Veritabanı başarıyla çekildi")
            return True
            
        except Exception as e:
            print(f"❌ Veritabanı çekme hatası: {e}")
            return False
    
    def verify_database(self, db_path):
        """Veritabanını doğrula"""
        try:
            conn = sqlite3.connect(db_path)
            
            # Tabloları kontrol et
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            if 'stage_analysis' not in tables:
                print("❌ stage_analysis tablosu bulunamadı")
                conn.close()
                return False
            
            # Kayıt sayısını kontrol et
            cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis")
            total = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1")
            yes_count = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis WHERE stage1_isSubscriptionCompany = 0")
            no_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ Veritabanı doğrulandı:")
            print(f"   📊 Toplam kayıt: {total}")
            print(f"   ✅ EVET: {yes_count}")
            print(f"   ❌ HAYIR: {no_count}")
            
            return total > 0
            
        except Exception as e:
            print(f"❌ Veritabanı doğrulama hatası: {e}")
            return False
    
    def backup_current_database(self):
        """Mevcut veritabanını yedekle"""
        if os.path.exists(self.local_db):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.sqlite"
            shutil.copy2(self.local_db, backup_name)
            print(f"💾 Mevcut veritabanı yedeklendi: {backup_name}")
    
    def sync_database(self):
        """Veritabanını senkronize et"""
        try:
            # Mevcut veritabanını yedekle
            self.backup_current_database()
            
            # Yeni veritabanını kopyala
            if os.path.exists(self.temp_db):
                shutil.copy2(self.temp_db, self.local_db)
                os.remove(self.temp_db)
                print(f"✅ Veritabanı güncellendi: {self.local_db}")
                return True
            else:
                print("❌ Geçici veritabanı dosyası bulunamadı")
                return False
                
        except Exception as e:
            print(f"❌ Senkronizasyon hatası: {e}")
            return False
    
    def full_sync(self):
        """Tam senkronizasyon işlemi"""
        print("🔄 Android Gerçek Veri Senkronizasyonu Başlıyor...")
        print("=" * 50)
        
        # 1. Cihaz bağlantısını kontrol et
        if not self.check_device_connection():
            return False
        
        # 2. Uygulamanın yüklü olduğunu kontrol et
        if not self.check_app_installed():
            return False
        
        # 3. Veritabanının var olduğunu kontrol et
        if not self.check_database_exists():
            return False
        
        # 4. Veritabanını çek
        if not self.pull_database():
            return False
        
        # 5. Veritabanını doğrula
        if not self.verify_database(self.temp_db):
            return False
        
        # 6. Senkronize et
        if not self.sync_database():
            return False
        
        print("\n🎉 Senkronizasyon başarıyla tamamlandı!")
        print("💻 Web uygulamasını başlatabilirsiniz: python web_viewer.py")
        return True

def main():
    """Ana fonksiyon"""
    print("📱➡️💻 Android Gerçek Veri Senkronizasyonu")
    print("=" * 50)
    
    sync = AndroidDataSync()
    
    print("\n📋 Mevcut Durum:")
    print(f"🔗 Hedef Cihaz: {sync.device_id}")
    print(f"📱 Uygulama: {sync.package_name}")
    print(f"🗄️ Veritabanı: {sync.db_name}")
    
    print("\n🚀 Senkronizasyon başlatılıyor...")
    
    if sync.full_sync():
        print("\n✅ İşlem tamamlandı!")
        print("🌐 Web arayüzünü açmak için: http://localhost:5000")
    else:
        print("\n❌ Senkronizasyon başarısız!")
        print("💡 Sorun giderme:")
        print("   1. Android uygulamasında email analizi yaptığınızdan emin olun")
        print("   2. Cihazın root'lu olduğundan emin olun")
        print("   3. USB debugging'in aktif olduğundan emin olun")

if __name__ == "__main__":
    main()
