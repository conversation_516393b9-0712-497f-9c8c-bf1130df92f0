1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.abonekaptanmobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" /> <!-- BU SATIRI EKLEDİK/DÜZELTTİK -->
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:6:5-67
11-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:7:5-79
12-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:7:22-76
13    <!--
14         GET_ACCOUNTS is not strictly needed for Google Sign-In on newer APIs,
15         but can be useful for account discovery on older devices.
16         The Google Sign-In library handles most of this.
17    -->
18    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
18-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:11:5-71
18-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:11:22-68
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:12:5-68
19-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:12:22-65
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
20-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
21-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
22
23    <permission
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
24        android:name="com.example.abonekaptanmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
25        android:protectionLevel="signature" />
25-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
26
27    <uses-permission android:name="com.example.abonekaptanmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
28
29    <application
29-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:14:5-54:19
30        android:name="com.example.abonekaptanmobile.AboneKaptanApp"
30-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:15:9-39
31        android:allowBackup="true"
31-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:16:9-35
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
33        android:dataExtractionRules="@xml/data_extraction_rules"
33-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:17:9-65
34        android:debuggable="true"
35        android:extractNativeLibs="false"
36        android:fullBackupContent="@xml/backup_rules"
36-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:18:9-54
37        android:icon="@mipmap/ic_launcher"
37-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:19:9-43
38        android:label="@string/app_name"
38-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:20:9-41
39        android:networkSecurityConfig="@xml/network_security_config"
39-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:24:9-69
40        android:roundIcon="@mipmap/ic_launcher_round"
40-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:21:9-54
41        android:supportsRtl="true"
41-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:22:9-35
42        android:theme="@style/Theme.AboneKaptanMobile"
42-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:23:9-55
43        android:usesCleartextTraffic="true" >
43-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:25:9-44
44        <activity
44-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:27:9-40:20
45            android:name="com.example.abonekaptanmobile.MainActivity"
45-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:28:13-41
46            android:exported="true"
46-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:29:13-36
47            android:label="@string/app_name"
47-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:30:13-45
48            android:theme="@style/Theme.AboneKaptanMobile" >
48-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:31:13-59
49            <intent-filter>
49-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:32:13-35:29
50                <action android:name="android.intent.action.MAIN" />
50-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:33:17-69
50-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:33:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:34:17-77
52-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:34:27-74
53            </intent-filter>
54            <!--
55                 Add this intent filter for Google Sign-In redirect, if needed by specific configurations
56                 Usually, Google Sign-In library handles this without explicit redirect URI in manifest for mobile.
57                 This is more common for web OAuth flows. For mobile, ensure your SHA-1 is in Firebase/Google Cloud.
58            -->
59        </activity>
60
61        <!-- WorkManager Initializer -->
62        <provider
63            android:name="androidx.startup.InitializationProvider"
63-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:44:13-67
64            android:authorities="com.example.abonekaptanmobile.androidx-startup"
64-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:45:13-68
65            android:exported="false" >
65-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:46:13-37
66            <meta-data
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
67                android:name="androidx.emoji2.text.EmojiCompatInitializer"
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
68                android:value="androidx.startup" />
68-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
69            <meta-data
69-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
70                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
71                android:value="androidx.startup" />
71-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
72            <meta-data
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
73                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
74                android:value="androidx.startup" />
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
75        </provider>
76
77        <activity
77-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
78            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
78-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
79            android:excludeFromRecents="true"
79-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
80            android:exported="false"
80-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
81            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
81-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
82        <!--
83            Service handling Google Sign-In user revocation. For apps that do not integrate with
84            Google Sign-In, this service will never be started.
85        -->
86        <service
86-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
87            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
87-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
88            android:exported="true"
88-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
89            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
89-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
90            android:visibleToInstantApps="true" />
90-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
91
92        <activity
92-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
93            android:name="com.google.android.gms.common.api.GoogleApiActivity"
93-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
94            android:exported="false"
94-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
95            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
95-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
96
97        <meta-data
97-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
98            android:name="com.google.android.gms.version"
98-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
99            android:value="@integer/google_play_services_version" />
99-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
100
101        <service
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
102            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
103            android:directBootAware="false"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
104            android:enabled="@bool/enable_system_alarm_service_default"
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
105            android:exported="false" />
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
106        <service
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
107            android:name="androidx.work.impl.background.systemjob.SystemJobService"
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
108            android:directBootAware="false"
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
109            android:enabled="@bool/enable_system_job_service_default"
109-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
110            android:exported="true"
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
111            android:permission="android.permission.BIND_JOB_SERVICE" />
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
112        <service
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
113            android:name="androidx.work.impl.foreground.SystemForegroundService"
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
114            android:directBootAware="false"
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
115            android:enabled="@bool/enable_system_foreground_service_default"
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
116            android:exported="false" />
116-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
117
118        <receiver
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
119            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
120            android:directBootAware="false"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
121            android:enabled="true"
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
122            android:exported="false" />
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
123        <receiver
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
124            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
125            android:directBootAware="false"
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
126            android:enabled="false"
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
127            android:exported="false" >
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
128            <intent-filter>
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
129                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
130                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
130-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
131            </intent-filter>
132        </receiver>
133        <receiver
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
134            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
135            android:directBootAware="false"
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
136            android:enabled="false"
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
137            android:exported="false" >
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
138            <intent-filter>
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
139                <action android:name="android.intent.action.BATTERY_OKAY" />
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
140                <action android:name="android.intent.action.BATTERY_LOW" />
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
140-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
141            </intent-filter>
142        </receiver>
143        <receiver
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
144            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
145            android:directBootAware="false"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
146            android:enabled="false"
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
147            android:exported="false" >
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
148            <intent-filter>
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
149                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
150                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
150-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
151            </intent-filter>
152        </receiver>
153        <receiver
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
154            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
155            android:directBootAware="false"
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
156            android:enabled="false"
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
157            android:exported="false" >
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
158            <intent-filter>
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
159                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
159-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
160            </intent-filter>
161        </receiver>
162        <receiver
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
163            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
164            android:directBootAware="false"
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
165            android:enabled="false"
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
166            android:exported="false" >
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
167            <intent-filter>
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
168                <action android:name="android.intent.action.BOOT_COMPLETED" />
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
169                <action android:name="android.intent.action.TIME_SET" />
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
170                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
170-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
171            </intent-filter>
172        </receiver>
173        <receiver
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
174            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
175            android:directBootAware="false"
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
176            android:enabled="@bool/enable_system_alarm_service_default"
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
177            android:exported="false" >
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
178            <intent-filter>
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
179                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
179-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
180            </intent-filter>
181        </receiver>
182        <receiver
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
183            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
184            android:directBootAware="false"
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
185            android:enabled="true"
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
186            android:exported="true"
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
187            android:permission="android.permission.DUMP" >
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
188            <intent-filter>
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
189                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
189-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
190            </intent-filter>
191        </receiver>
192
193        <activity
193-->[androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
194            android:name="androidx.compose.ui.tooling.PreviewActivity"
194-->[androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
195            android:exported="true" />
195-->[androidx.compose.ui:ui-tooling-android:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\6ac865f2e376b0e35b433497d52ff6a4\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
196        <activity
196-->[androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:23:9-25:39
197            android:name="androidx.activity.ComponentActivity"
197-->[androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:24:13-63
198            android:exported="true" />
198-->[androidx.compose.ui:ui-test-manifest:1.6.2] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7fbbb40529799f840f5efdd34f185d49\transformed\ui-test-manifest-1.6.2\AndroidManifest.xml:25:13-36
199
200        <service
200-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
201            android:name="androidx.room.MultiInstanceInvalidationService"
201-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
202            android:directBootAware="true"
202-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
203            android:exported="false" />
203-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
204
205        <receiver
205-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
206            android:name="androidx.profileinstaller.ProfileInstallReceiver"
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
207            android:directBootAware="false"
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
208            android:enabled="true"
208-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
209            android:exported="true"
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
210            android:permission="android.permission.DUMP" >
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
211            <intent-filter>
211-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
212                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
213            </intent-filter>
214            <intent-filter>
214-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
215                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
215-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
216            </intent-filter>
217            <intent-filter>
217-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
218                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
218-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
219            </intent-filter>
220            <intent-filter>
220-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
221                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
221-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
222            </intent-filter>
223        </receiver>
224    </application>
225
226</manifest>
