// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.services;

import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.GroqRepository;
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SubscriptionClassifier_Factory implements Factory<SubscriptionClassifier> {
  private final Provider<CommunityPatternRepository> communityPatternRepoProvider;

  private final Provider<GroqRepository> groqRepositoryProvider;

  private final Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider;

  public SubscriptionClassifier_Factory(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<GroqRepository> groqRepositoryProvider,
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    this.communityPatternRepoProvider = communityPatternRepoProvider;
    this.groqRepositoryProvider = groqRepositoryProvider;
    this.stageAnalysisRepositoryProvider = stageAnalysisRepositoryProvider;
  }

  @Override
  public SubscriptionClassifier get() {
    return newInstance(communityPatternRepoProvider.get(), groqRepositoryProvider.get(), stageAnalysisRepositoryProvider.get());
  }

  public static SubscriptionClassifier_Factory create(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<GroqRepository> groqRepositoryProvider,
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    return new SubscriptionClassifier_Factory(communityPatternRepoProvider, groqRepositoryProvider, stageAnalysisRepositoryProvider);
  }

  public static SubscriptionClassifier newInstance(CommunityPatternRepository communityPatternRepo,
      GroqRepository groqRepository, StageAnalysisRepository stageAnalysisRepository) {
    return new SubscriptionClassifier(communityPatternRepo, groqRepository, stageAnalysisRepository);
  }
}
