package com.example.abonekaptanmobile.data.local.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Turkish: E-posta veritabanı entity'si.
 * English: Email database entity.
 */
@Entity(tableName = "emails")
data class EmailEntity(
    @PrimaryKey
    val id: String,
    val from: String,
    val subject: String,
    val snippet: String,
    val bodyPlainText: String? = null,
    val bodySnippet: String? = null,
    val date: Long,
    val threadId: String,
    val labelIds: String // Comma-separated list of label IDs
)
