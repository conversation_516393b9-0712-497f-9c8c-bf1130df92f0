package com.example.abonekaptanmobile.data.local.entity;

import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0006\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\t\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\n"}, d2 = {"Lcom/example/abonekaptanmobile/data/local/entity/PatternType;", "", "()V", "BODY_KEYWORD", "", "COMBINED", "DOMAIN", "SENDER_EMAIL", "SUBJECT_KEYWORD", "UNKNOWN", "app_release"})
public final class PatternType {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String UNKNOWN = "unknown";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String DOMAIN = "domain";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SENDER_EMAIL = "sender_email";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String SUBJECT_KEYWORD = "subject_keyword";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String BODY_KEYWORD = "body_keyword";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String COMBINED = "combined";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.data.local.entity.PatternType INSTANCE = null;
    
    private PatternType() {
        super();
    }
}