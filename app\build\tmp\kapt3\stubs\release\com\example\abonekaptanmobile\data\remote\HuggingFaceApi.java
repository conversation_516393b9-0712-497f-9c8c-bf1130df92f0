package com.example.abonekaptanmobile.data.remote;

import com.example.abonekaptanmobile.data.remote.model.HuggingFaceRequest;
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceResponse;
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest;
import com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationResponse;
import com.example.abonekaptanmobile.data.remote.model.LlamaRequest;
import com.example.abonekaptanmobile.data.remote.model.LlamaResponse;
import retrofit2.http.Body;
import retrofit2.http.Header;
import retrofit2.http.POST;

/**
 * Turkish: Hugging Face API için Retrofit arayüzü.
 * English: Retrofit interface for Hugging Face API.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0010$\n\u0002\u0018\u0002\n\u0002\b\u0002\bf\u0018\u00002\u00020\u0001J\"\u0010\u0002\u001a\u00020\u00032\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ4\u0010\t\u001a\u0014\u0012\u0010\u0012\u000e\u0012\u0004\u0012\u00020\u0005\u0012\u0004\u0012\u00020\u00050\u000b0\n2\b\b\u0001\u0010\u0004\u001a\u00020\u00052\b\b\u0001\u0010\u0006\u001a\u00020\fH\u00a7@\u00a2\u0006\u0002\u0010\r\u00a8\u0006\u000e"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/HuggingFaceApi;", "", "classifyText", "Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceResponse;", "authToken", "", "request", "Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceRequest;", "(Ljava/lang/String;Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "generateText", "", "", "Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceTextGenerationRequest;", "(Ljava/lang/String;Lcom/example/abonekaptanmobile/data/remote/model/HuggingFaceTextGenerationRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public abstract interface HuggingFaceApi {
    
    /**
     * Turkish: Zero-shot sınıflandırma için Hugging Face API'sine istek gönderir.
     * English: Sends a request to Hugging Face API for zero-shot classification.
     */
    @retrofit2.http.POST(value = "models/facebook/bart-large-mnli")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object classifyText(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authToken, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.model.HuggingFaceRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.remote.model.HuggingFaceResponse> $completion);
    
    /**
     * Turkish: Hugging Face Llama modeli ile text generation için istek gönderir.
     * English: Sends a request to Hugging Face Llama model for text generation.
     */
    @retrofit2.http.POST(value = "models/meta-llama/Llama-3.3-70B-Instruct")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object generateText(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authToken, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.remote.model.HuggingFaceTextGenerationRequest request, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<? extends java.util.Map<java.lang.String, java.lang.String>>> $completion);
}