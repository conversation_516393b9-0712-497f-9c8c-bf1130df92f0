package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

/**
 * Turkish: Batch işleme sonucu.
 * English: Batch processing result.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000,\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\b\u0018\u0000 \u00112\u00020\u0001:\u0001\u0011B\u0013\u0012\f\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\u0002\u0010\u0005J\u000f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0003J\u0019\u0010\t\u001a\u00020\u00002\u000e\b\u0002\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003H\u00c6\u0001J\u0013\u0010\n\u001a\u00020\u000b2\b\u0010\f\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0017\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\u00a8\u0006\u0012"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/BatchAnalysisResult;", "", "results", "", "Lcom/example/abonekaptanmobile/data/remote/model/TwoStageAnalysisResult;", "(Ljava/util/List;)V", "getResults", "()Ljava/util/List;", "component1", "copy", "equals", "", "other", "hashCode", "", "toString", "", "Companion", "app_debug"})
public final class BatchAnalysisResult {
    @org.jetbrains.annotations.NotNull()
    private final java.util.List<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult> results = null;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.abonekaptanmobile.data.remote.model.BatchAnalysisResult.Companion Companion = null;
    
    public BatchAnalysisResult(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult> results) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult> getResults() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult> component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.BatchAnalysisResult copy(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult> results) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001c\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bJ$\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u000b0\b2\u0006\u0010\f\u001a\u00020\r2\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\t0\bH\u0002J\u0018\u0010\u000e\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\tH\u0002\u00a8\u0006\u0011"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/BatchAnalysisResult$Companion;", "", "()V", "fromGroqResponse", "Lcom/example/abonekaptanmobile/data/remote/model/BatchAnalysisResult;", "response", "Lcom/example/abonekaptanmobile/data/remote/model/GroqChatResponse;", "emailInfos", "", "Lcom/example/abonekaptanmobile/data/remote/model/BatchEmailInfo;", "parseBatchJsonResponse", "Lcom/example/abonekaptanmobile/data/remote/model/TwoStageAnalysisResult;", "content", "", "parseSingleBatchResult", "jsonStr", "emailInfo", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.abonekaptanmobile.data.remote.model.BatchAnalysisResult fromGroqResponse(@org.jetbrains.annotations.NotNull()
        com.example.abonekaptanmobile.data.remote.model.GroqChatResponse response, @org.jetbrains.annotations.NotNull()
        java.util.List<com.example.abonekaptanmobile.data.remote.model.BatchEmailInfo> emailInfos) {
            return null;
        }
        
        private final java.util.List<com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult> parseBatchJsonResponse(java.lang.String content, java.util.List<com.example.abonekaptanmobile.data.remote.model.BatchEmailInfo> emailInfos) {
            return null;
        }
        
        private final com.example.abonekaptanmobile.data.remote.model.TwoStageAnalysisResult parseSingleBatchResult(java.lang.String jsonStr, com.example.abonekaptanmobile.data.remote.model.BatchEmailInfo emailInfo) {
            return null;
        }
    }
}