// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.remote.GroqApi;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class GroqRepository_Factory implements Factory<GroqRepository> {
  private final Provider<GroqApi> groqApiProvider;

  public GroqRepository_Factory(Provider<GroqApi> groqApiProvider) {
    this.groqApiProvider = groqApiProvider;
  }

  @Override
  public GroqRepository get() {
    return newInstance(groqApiProvider.get());
  }

  public static GroqRepository_Factory create(Provider<GroqApi> groqApiProvider) {
    return new GroqRepository_Factory(groqApiProvider);
  }

  public static GroqRepository newInstance(GroqApi groqApi) {
    return new GroqRepository(groqApi);
  }
}
