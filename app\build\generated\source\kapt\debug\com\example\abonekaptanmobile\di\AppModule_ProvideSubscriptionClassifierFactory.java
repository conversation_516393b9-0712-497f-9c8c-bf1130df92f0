// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.repository.CommunityPatternRepository;
import com.example.abonekaptanmobile.data.repository.GroqRepository;
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository;
import com.example.abonekaptanmobile.services.SubscriptionClassifier;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideSubscriptionClassifierFactory implements Factory<SubscriptionClassifier> {
  private final Provider<CommunityPatternRepository> communityPatternRepoProvider;

  private final Provider<GroqRepository> groqRepositoryProvider;

  private final Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider;

  public AppModule_ProvideSubscriptionClassifierFactory(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<GroqRepository> groqRepositoryProvider,
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    this.communityPatternRepoProvider = communityPatternRepoProvider;
    this.groqRepositoryProvider = groqRepositoryProvider;
    this.stageAnalysisRepositoryProvider = stageAnalysisRepositoryProvider;
  }

  @Override
  public SubscriptionClassifier get() {
    return provideSubscriptionClassifier(communityPatternRepoProvider.get(), groqRepositoryProvider.get(), stageAnalysisRepositoryProvider.get());
  }

  public static AppModule_ProvideSubscriptionClassifierFactory create(
      Provider<CommunityPatternRepository> communityPatternRepoProvider,
      Provider<GroqRepository> groqRepositoryProvider,
      Provider<StageAnalysisRepository> stageAnalysisRepositoryProvider) {
    return new AppModule_ProvideSubscriptionClassifierFactory(communityPatternRepoProvider, groqRepositoryProvider, stageAnalysisRepositoryProvider);
  }

  public static SubscriptionClassifier provideSubscriptionClassifier(
      CommunityPatternRepository communityPatternRepo, GroqRepository groqRepository,
      StageAnalysisRepository stageAnalysisRepository) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideSubscriptionClassifier(communityPatternRepo, groqRepository, stageAnalysisRepository));
  }
}
