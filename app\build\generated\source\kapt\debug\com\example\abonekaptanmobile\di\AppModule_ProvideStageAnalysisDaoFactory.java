// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideStageAnalysisDaoFactory implements Factory<StageAnalysisDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public AppModule_ProvideStageAnalysisDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public StageAnalysisDao get() {
    return provideStageAnalysisDao(appDatabaseProvider.get());
  }

  public static AppModule_ProvideStageAnalysisDaoFactory create(
      Provider<AppDatabase> appDatabaseProvider) {
    return new AppModule_ProvideStageAnalysisDaoFactory(appDatabaseProvider);
  }

  public static StageAnalysisDao provideStageAnalysisDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideStageAnalysisDao(appDatabase));
  }
}
