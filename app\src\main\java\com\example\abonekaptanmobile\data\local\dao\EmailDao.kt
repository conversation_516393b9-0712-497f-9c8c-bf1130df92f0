package com.example.abonekaptanmobile.data.local.dao

import androidx.room.*
import com.example.abonekaptanmobile.data.local.entity.EmailEntity
import kotlinx.coroutines.flow.Flow

/**
 * Turkish: E-posta veritabanı erişim ne<PERSON>.
 * English: Email database access object.
 */
@Dao
interface EmailDao {

    /**
     * Turkish: E-postaları veritabanına ekle.
     * English: Insert emails into database.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmails(emails: List<EmailEntity>)

    /**
     * Turkish: Tek e-posta ekle.
     * English: Insert single email.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertEmail(email: EmailEntity)

    /**
     * Turkish: Tüm e-postaları getir.
     * English: Get all emails.
     */
    @Query("SELECT * FROM emails ORDER BY date DESC")
    fun getAllEmails(): Flow<List<EmailEntity>>

    /**
     * Turkish: E-posta ID'sine göre getir.
     * English: Get email by ID.
     */
    @Query("SELECT * FROM emails WHERE id = :emailId")
    suspend fun getEmailById(emailId: String): EmailEntity?

    /**
     * Turkish: Gönderen adresine göre e-postaları getir.
     * English: Get emails by sender address.
     */
    @Query("SELECT * FROM emails WHERE `from` LIKE :senderPattern ORDER BY date DESC")
    fun getEmailsBySender(senderPattern: String): Flow<List<EmailEntity>>

    /**
     * Turkish: Konu başlığına göre e-postaları getir.
     * English: Get emails by subject.
     */
    @Query("SELECT * FROM emails WHERE subject LIKE :subjectPattern ORDER BY date DESC")
    fun getEmailsBySubject(subjectPattern: String): Flow<List<EmailEntity>>

    /**
     * Turkish: Tarih aralığına göre e-postaları getir.
     * English: Get emails by date range.
     */
    @Query("SELECT * FROM emails WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getEmailsByDateRange(startDate: Long, endDate: Long): Flow<List<EmailEntity>>

    /**
     * Turkish: E-posta sayısını getir.
     * English: Get email count.
     */
    @Query("SELECT COUNT(*) FROM emails")
    suspend fun getEmailCount(): Int

    /**
     * Turkish: E-posta güncelle.
     * English: Update email.
     */
    @Update
    suspend fun updateEmail(email: EmailEntity)

    /**
     * Turkish: E-posta sil.
     * English: Delete email.
     */
    @Delete
    suspend fun deleteEmail(email: EmailEntity)

    /**
     * Turkish: E-posta ID'sine göre sil.
     * English: Delete email by ID.
     */
    @Query("DELETE FROM emails WHERE id = :emailId")
    suspend fun deleteEmailById(emailId: String)

    /**
     * Turkish: Tüm e-postaları sil.
     * English: Delete all emails.
     */
    @Query("DELETE FROM emails")
    suspend fun deleteAllEmails()
}
