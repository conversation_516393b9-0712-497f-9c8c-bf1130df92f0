package com.example.abonekaptanmobile.ui.viewmodel;

import android.content.Context;
import android.content.Intent;
import android.util.Log;
import androidx.activity.result.ActivityResultLauncher;
import androidx.lifecycle.ViewModel;
import androidx.work.*;
import com.example.abonekaptanmobile.auth.GoogleAuthManager;
import com.example.abonekaptanmobile.data.local.entity.FeedbackEntity;
import com.example.abonekaptanmobile.data.repository.FeedbackRepository;
import com.example.abonekaptanmobile.data.repository.GmailRepository;
import com.example.abonekaptanmobile.data.repository.EmailRepository;
import com.example.abonekaptanmobile.data.repository.SubscriptionRepository;
import com.example.abonekaptanmobile.model.RawEmail;
import com.example.abonekaptanmobile.model.SubscriptionItem;
import com.example.abonekaptanmobile.model.SubscriptionStatus;
import com.example.abonekaptanmobile.services.SubscriptionClassifier;
import com.example.abonekaptanmobile.utils.HybridApproachTester;
import com.example.abonekaptanmobile.workers.ProcessFeedbackWorker;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.tasks.Task;
import dagger.hilt.android.lifecycle.HiltViewModel;
import dagger.hilt.android.qualifiers.ApplicationContext;
import kotlinx.coroutines.flow.*;
import java.util.concurrent.TimeUnit;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0098\u0001\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0004\b\u0007\u0018\u00002\u00020\u0001BI\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u0012\u0006\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\u0002\u0010\u0012J\u0006\u00101\u001a\u000202J\u0016\u00103\u001a\u0002022\u000e\u00104\u001a\n\u0012\u0004\u0012\u000206\u0018\u000105J\u0006\u00107\u001a\u000202J\b\u00108\u001a\u000202H\u0002J\u0006\u00109\u001a\u000202J\u0014\u0010:\u001a\u0002022\f\u0010;\u001a\b\u0012\u0004\u0012\u00020=0<J(\u0010>\u001a\u0002022\u0006\u0010?\u001a\u00020\u00172\u0006\u0010@\u001a\u00020A2\u0006\u0010B\u001a\u00020\u00172\b\u0010C\u001a\u0004\u0018\u00010\u0017J\u0006\u0010D\u001a\u000202R\u0014\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00150\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0016\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0016\u0010\u0018\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00170\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0014\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u001a0\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001a\u0010\u001d\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0\u0014X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010 \u001a\b\u0012\u0004\u0012\u00020\u00150!\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010#R\u0019\u0010$\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00170!\u00a2\u0006\b\n\u0000\u001a\u0004\b%\u0010#R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0019\u0010&\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00170!\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010#R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0010\u001a\u00020\u0011X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010(\u001a\b\u0012\u0004\u0012\u00020\u001a0!\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010#R\u0017\u0010)\u001a\b\u0012\u0004\u0012\u00020\u001a0!\u00a2\u0006\b\n\u0000\u001a\u0004\b)\u0010#R\u0017\u0010*\u001a\b\u0012\u0004\u0012\u00020\u001a0!\u00a2\u0006\b\n\u0000\u001a\u0004\b*\u0010#R\u0017\u0010+\u001a\b\u0012\u0004\u0012\u00020\u001a0!\u00a2\u0006\b\n\u0000\u001a\u0004\b+\u0010#R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010,\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u001f0\u001e0!\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010#R\u001d\u0010.\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020/0\u001e0!\u00a2\u0006\b\n\u0000\u001a\u0004\b0\u0010#\u00a8\u0006E"}, d2 = {"Lcom/example/abonekaptanmobile/ui/viewmodel/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "context", "Landroid/content/Context;", "googleAuthManager", "Lcom/example/abonekaptanmobile/auth/GoogleAuthManager;", "gmailRepository", "Lcom/example/abonekaptanmobile/data/repository/GmailRepository;", "emailRepository", "Lcom/example/abonekaptanmobile/data/repository/EmailRepository;", "subscriptionRepository", "Lcom/example/abonekaptanmobile/data/repository/SubscriptionRepository;", "subscriptionClassifier", "Lcom/example/abonekaptanmobile/services/SubscriptionClassifier;", "feedbackRepository", "Lcom/example/abonekaptanmobile/data/repository/FeedbackRepository;", "hybridApproachTester", "Lcom/example/abonekaptanmobile/utils/HybridApproachTester;", "(Landroid/content/Context;Lcom/example/abonekaptanmobile/auth/GoogleAuthManager;Lcom/example/abonekaptanmobile/data/repository/GmailRepository;Lcom/example/abonekaptanmobile/data/repository/EmailRepository;Lcom/example/abonekaptanmobile/data/repository/SubscriptionRepository;Lcom/example/abonekaptanmobile/services/SubscriptionClassifier;Lcom/example/abonekaptanmobile/data/repository/FeedbackRepository;Lcom/example/abonekaptanmobile/utils/HybridApproachTester;)V", "_emailProcessingProgress", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "_emailProcessingStatus", "", "_error", "_isLoading", "", "_isSignedIn", "_isSigningIn", "_subscriptions", "", "Lcom/example/abonekaptanmobile/model/SubscriptionItem;", "emailProcessingProgress", "Lkotlinx/coroutines/flow/StateFlow;", "getEmailProcessingProgress", "()Lkotlinx/coroutines/flow/StateFlow;", "emailProcessingStatus", "getEmailProcessingStatus", "error", "getError", "isLoading", "isSignedIn", "isSigningIn", "isTestingInProgress", "subscriptions", "getSubscriptions", "testResults", "Lcom/example/abonekaptanmobile/utils/TestResult;", "getTestResults", "clearError", "", "handleSignInResult", "task", "Lcom/google/android/gms/tasks/Task;", "Lcom/google/android/gms/auth/api/signin/GoogleSignInAccount;", "loadSubscriptions", "scheduleFeedbackWorker", "signOut", "startSignIn", "launcher", "Landroidx/activity/result/ActivityResultLauncher;", "Landroid/content/Intent;", "submitFeedback", "serviceName", "originalStatus", "Lcom/example/abonekaptanmobile/model/SubscriptionStatus;", "feedbackLabel", "note", "testHybridApproach", "app_release"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.auth.GoogleAuthManager googleAuthManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.GmailRepository gmailRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.EmailRepository emailRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.SubscriptionRepository subscriptionRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.services.SubscriptionClassifier subscriptionClassifier = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.repository.FeedbackRepository feedbackRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.utils.HybridApproachTester hybridApproachTester = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isSigningIn = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSigningIn = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isSignedIn = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSignedIn = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Boolean> _isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem>> _subscriptions = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem>> subscriptions = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> error = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.Float> _emailProcessingProgress = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Float> emailProcessingProgress = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<java.lang.String> _emailProcessingStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.String> emailProcessingStatus = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.utils.TestResult>> testResults = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isTestingInProgress = null;
    
    @javax.inject.Inject()
    public MainViewModel(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.auth.GoogleAuthManager googleAuthManager, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.GmailRepository gmailRepository, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.EmailRepository emailRepository, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.SubscriptionRepository subscriptionRepository, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.services.SubscriptionClassifier subscriptionClassifier, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.repository.FeedbackRepository feedbackRepository, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.utils.HybridApproachTester hybridApproachTester) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSigningIn() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isSignedIn() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isLoading() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.model.SubscriptionItem>> getSubscriptions() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getError() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Float> getEmailProcessingProgress() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.String> getEmailProcessingStatus() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.example.abonekaptanmobile.utils.TestResult>> getTestResults() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<java.lang.Boolean> isTestingInProgress() {
        return null;
    }
    
    public final void startSignIn(@org.jetbrains.annotations.NotNull()
    androidx.activity.result.ActivityResultLauncher<android.content.Intent> launcher) {
    }
    
    public final void handleSignInResult(@org.jetbrains.annotations.Nullable()
    com.google.android.gms.tasks.Task<com.google.android.gms.auth.api.signin.GoogleSignInAccount> task) {
    }
    
    public final void signOut() {
    }
    
    public final void loadSubscriptions() {
    }
    
    public final void submitFeedback(@org.jetbrains.annotations.NotNull()
    java.lang.String serviceName, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.model.SubscriptionStatus originalStatus, @org.jetbrains.annotations.NotNull()
    java.lang.String feedbackLabel, @org.jetbrains.annotations.Nullable()
    java.lang.String note) {
    }
    
    public final void clearError() {
    }
    
    /**
     * Turkish: Hybrid Approach sistemini test eder.
     * English: Tests the Hybrid Approach system.
     */
    public final void testHybridApproach() {
    }
    
    private final void scheduleFeedbackWorker() {
    }
}