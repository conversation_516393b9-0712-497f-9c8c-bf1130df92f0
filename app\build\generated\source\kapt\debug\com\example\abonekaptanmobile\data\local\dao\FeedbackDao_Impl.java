package com.example.abonekaptanmobile.data.local.dao;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.abonekaptanmobile.data.local.entity.FeedbackEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@SuppressWarnings({"unchecked", "deprecation"})
public final class FeedbackDao_Impl implements FeedbackDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<FeedbackEntity> __insertionAdapterOfFeedbackEntity;

  private final SharedSQLiteStatement __preparedStmtOfMarkFeedbackAsProcessed;

  private final SharedSQLiteStatement __preparedStmtOfClearAllFeedback;

  public FeedbackDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfFeedbackEntity = new EntityInsertionAdapter<FeedbackEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `feedback` (`id`,`serviceName`,`originalStatus`,`feedbackLabel`,`feedbackNote`,`createdAt`,`processed`) VALUES (nullif(?, 0),?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final FeedbackEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getServiceName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getServiceName());
        }
        if (entity.getOriginalStatus() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getOriginalStatus());
        }
        if (entity.getFeedbackLabel() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getFeedbackLabel());
        }
        if (entity.getFeedbackNote() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getFeedbackNote());
        }
        statement.bindLong(6, entity.getCreatedAt());
        final int _tmp = entity.getProcessed() ? 1 : 0;
        statement.bindLong(7, _tmp);
      }
    };
    this.__preparedStmtOfMarkFeedbackAsProcessed = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE feedback SET processed = 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfClearAllFeedback = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM feedback";
        return _query;
      }
    };
  }

  @Override
  public Object insertFeedback(final FeedbackEntity feedback,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfFeedbackEntity.insert(feedback);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, arg1);
  }

  @Override
  public Object markFeedbackAsProcessed(final long feedbackId,
      final Continuation<? super Unit> arg1) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfMarkFeedbackAsProcessed.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, feedbackId);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfMarkFeedbackAsProcessed.release(_stmt);
        }
      }
    }, arg1);
  }

  @Override
  public Object clearAllFeedback(final Continuation<? super Unit> arg0) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfClearAllFeedback.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfClearAllFeedback.release(_stmt);
        }
      }
    }, arg0);
  }

  @Override
  public Object getPendingFeedback(final Continuation<? super List<FeedbackEntity>> arg0) {
    final String _sql = "SELECT * FROM feedback WHERE processed = 0 ORDER BY createdAt ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<FeedbackEntity>>() {
      @Override
      @NonNull
      public List<FeedbackEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfOriginalStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "originalStatus");
          final int _cursorIndexOfFeedbackLabel = CursorUtil.getColumnIndexOrThrow(_cursor, "feedbackLabel");
          final int _cursorIndexOfFeedbackNote = CursorUtil.getColumnIndexOrThrow(_cursor, "feedbackNote");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "processed");
          final List<FeedbackEntity> _result = new ArrayList<FeedbackEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final FeedbackEntity _item;
            final long _tmpId;
            _tmpId = _cursor.getLong(_cursorIndexOfId);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            final String _tmpOriginalStatus;
            if (_cursor.isNull(_cursorIndexOfOriginalStatus)) {
              _tmpOriginalStatus = null;
            } else {
              _tmpOriginalStatus = _cursor.getString(_cursorIndexOfOriginalStatus);
            }
            final String _tmpFeedbackLabel;
            if (_cursor.isNull(_cursorIndexOfFeedbackLabel)) {
              _tmpFeedbackLabel = null;
            } else {
              _tmpFeedbackLabel = _cursor.getString(_cursorIndexOfFeedbackLabel);
            }
            final String _tmpFeedbackNote;
            if (_cursor.isNull(_cursorIndexOfFeedbackNote)) {
              _tmpFeedbackNote = null;
            } else {
              _tmpFeedbackNote = _cursor.getString(_cursorIndexOfFeedbackNote);
            }
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final boolean _tmpProcessed;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfProcessed);
            _tmpProcessed = _tmp != 0;
            _item = new FeedbackEntity(_tmpId,_tmpServiceName,_tmpOriginalStatus,_tmpFeedbackLabel,_tmpFeedbackNote,_tmpCreatedAt,_tmpProcessed);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, arg0);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
