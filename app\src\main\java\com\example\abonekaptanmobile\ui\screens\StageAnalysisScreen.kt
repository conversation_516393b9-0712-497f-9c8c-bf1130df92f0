package com.example.abonekaptanmobile.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.HourglassEmpty
import androidx.compose.material.icons.filled.Info
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity
import com.example.abonekaptanmobile.ui.viewmodel.StageAnalysisViewModel
import java.text.SimpleDateFormat
import java.util.*

/**
 * Turkish: İki aşamalı analiz sonuçlarını gösteren ekran.
 * English: Screen showing two-stage analysis results.
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StageAnalysisScreen(
    viewModel: StageAnalysisViewModel = hiltViewModel()
) {
    val analysisResults by viewModel.analysisResults.collectAsState()
    val statistics by viewModel.statistics.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    var selectedTab by remember { mutableStateOf(0) }
    val tabs = listOf("Tüm Sonuçlar", "Aşama 1", "Aşama 2", "İstatistikler")

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Başlık
        Text(
            text = "İki Aşamalı E-posta Analizi",
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        // Tab Row
        TabRow(
            selectedTabIndex = selectedTab,
            modifier = Modifier.fillMaxWidth()
        ) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTab == index,
                    onClick = { selectedTab = index },
                    text = { Text(title) }
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            when (selectedTab) {
                0 -> AllResultsTab(analysisResults)
                1 -> Stage1Tab(analysisResults.filter { it.stage1_isSubscriptionCompany })
                2 -> Stage2Tab(analysisResults.filter { it.processingStatus == "STAGE2_COMPLETED" })
                3 -> StatisticsTab(statistics, analysisResults)
            }
        }
    }
}

@Composable
private fun AllResultsTab(results: List<StageAnalysisEntity>) {
    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(results) { result ->
            AnalysisResultCard(result)
        }
    }
}

@Composable
private fun Stage1Tab(results: List<StageAnalysisEntity>) {
    Column {
        Text(
            text = "Aşama 1'i Geçen E-postalar (${results.size})",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(results) { result ->
                Stage1ResultCard(result)
            }
        }
    }
}

@Composable
private fun Stage2Tab(results: List<StageAnalysisEntity>) {
    Column {
        Text(
            text = "Aşama 2'yi Tamamlayan E-postalar (${results.size})",
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )
        
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(results) { result ->
                Stage2ResultCard(result)
            }
        }
    }
}

@Composable
private fun StatisticsTab(
    statistics: com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics?,
    results: List<StageAnalysisEntity>
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        statistics?.let { stats ->
            StatisticsCard(
                title = "Genel İstatistikler",
                items = listOf(
                    "Toplam E-posta" to stats.totalEmails.toString(),
                    "Aşama 1 Geçen" to stats.stage1Passed.toString(),
                    "Aşama 2 Tamamlanan" to stats.stage2Completed.toString(),
                    "Ortalama Güven" to String.format("%.2f", stats.avgConfidence)
                )
            )
        }

        val companyStats = results
            .filter { it.stage1_isSubscriptionCompany }
            .groupBy { it.stage1_companyName }
            .map { (company, emails) ->
                company to emails.size
            }
            .sortedByDescending { it.second }
            .take(10)

        if (companyStats.isNotEmpty()) {
            StatisticsCard(
                title = "En Çok Tespit Edilen Şirketler",
                items = companyStats.map { "${it.first}" to "${it.second} e-posta" }
            )
        }
    }
}

@Composable
private fun AnalysisResultCard(result: StageAnalysisEntity) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = result.subject,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                ProcessingStatusIcon(result.processingStatus)
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Gönderen: ${result.from}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "Domain: ${result.domain}",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            if (result.stage1_isSubscriptionCompany) {
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Şirket: ${result.stage1_companyName}",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                    
                    Text(
                        text = "Güven: ${String.format("%.2f", result.stage1_companyConfidence)}",
                        style = MaterialTheme.typography.bodyMedium,
                        color = if (result.stage1_companyConfidence >= 0.8f) 
                            MaterialTheme.colorScheme.primary 
                        else MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                result.stage2_emailType?.let { emailType ->
                    Text(
                        text = "E-posta Türü: $emailType",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.secondary
                    )
                }
            }
        }
    }
}

@Composable
private fun Stage1ResultCard(result: StageAnalysisEntity) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Text(
                text = result.stage1_companyName,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Text(
                text = result.subject,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Güven: ${String.format("%.2f", result.stage1_companyConfidence)}",
                    style = MaterialTheme.typography.bodySmall
                )
                
                Text(
                    text = formatDate(result.date),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun Stage2ResultCard(result: StageAnalysisEntity) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.secondaryContainer.copy(alpha = 0.3f)
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = result.stage1_companyName,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                result.stage2_emailType?.let { type ->
                    EmailTypeChip(type)
                }
            }
            
            Text(
                text = result.subject,
                style = MaterialTheme.typography.bodyMedium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
            
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                result.stage2_emailTypeConfidence?.let { confidence ->
                    Text(
                        text = "Güven: ${String.format("%.2f", confidence)}",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                Text(
                    text = formatDate(result.date),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun StatisticsCard(
    title: String,
    items: List<Pair<String, String>>
) {
    Card(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 12.dp)
            )
            
            items.forEach { (label, value) ->
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = label,
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Text(
                        text = value,
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun ProcessingStatusIcon(status: String) {
    val (icon, color) = when (status) {
        "STAGE1_COMPLETED" -> Icons.Default.HourglassEmpty to MaterialTheme.colorScheme.primary
        "STAGE2_COMPLETED" -> Icons.Default.CheckCircle to MaterialTheme.colorScheme.primary
        "STAGE1_FAILED" -> Icons.Default.Error to MaterialTheme.colorScheme.error
        else -> Icons.Default.Info to MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Icon(
        imageVector = icon,
        contentDescription = status,
        tint = color,
        modifier = Modifier.size(20.dp)
    )
}

@Composable
private fun EmailTypeChip(type: String) {
    val (text, color) = when (type) {
        "subscription_start" -> "Başlangıç" to MaterialTheme.colorScheme.primary
        "subscription_cancel" -> "İptal" to MaterialTheme.colorScheme.error
        else -> type to MaterialTheme.colorScheme.onSurfaceVariant
    }
    
    Surface(
        shape = RoundedCornerShape(12.dp),
        color = color.copy(alpha = 0.1f),
        modifier = Modifier.padding(4.dp)
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelSmall,
            color = color,
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
        )
    }
}

private fun formatDate(timestamp: Long): String {
    val sdf = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
    return sdf.format(Date(timestamp))
}
