package com.example.abonekaptanmobile.data.local.dao

import androidx.room.*
import com.example.abonekaptanmobile.data.local.entity.SubscriptionEntity
import kotlinx.coroutines.flow.Flow

/**
 * Turkish: Abonelik veritabanı erişim nesnesi.
 * English: Subscription database access object.
 */
@Dao
interface SubscriptionDao {

    /**
     * Turkish: Abonelikleri veritabanına ekle.
     * English: Insert subscriptions into database.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSubscriptions(subscriptions: List<SubscriptionEntity>)

    /**
     * Turkish: Tek abonelik ekle.
     * English: Insert single subscription.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSubscription(subscription: SubscriptionEntity)

    /**
     * Turkish: Tüm abonelikleri getir.
     * English: Get all subscriptions.
     */
    @Query("SELECT * FROM subscriptions ORDER BY lastEmailDate DESC")
    fun getAllSubscriptions(): Flow<List<SubscriptionEntity>>

    /**
     * Turkish: Aktif abonelikleri getir.
     * English: Get active subscriptions.
     */
    @Query("SELECT * FROM subscriptions WHERE status = 'ACTIVE' ORDER BY lastEmailDate DESC")
    fun getActiveSubscriptions(): Flow<List<SubscriptionEntity>>

    /**
     * Turkish: İptal edilmiş abonelikleri getir.
     * English: Get cancelled subscriptions.
     */
    @Query("SELECT * FROM subscriptions WHERE status = 'CANCELLED' ORDER BY lastEmailDate DESC")
    fun getCancelledSubscriptions(): Flow<List<SubscriptionEntity>>

    /**
     * Turkish: Bilinmeyen durumlu abonelikleri getir.
     * English: Get unknown status subscriptions.
     */
    @Query("SELECT * FROM subscriptions WHERE status = 'UNKNOWN' ORDER BY lastEmailDate DESC")
    fun getUnknownSubscriptions(): Flow<List<SubscriptionEntity>>

    /**
     * Turkish: Servis adına göre abonelik getir.
     * English: Get subscription by service name.
     */
    @Query("SELECT * FROM subscriptions WHERE serviceName = :serviceName")
    suspend fun getSubscriptionByServiceName(serviceName: String): SubscriptionEntity?

    /**
     * Turkish: Abonelik sayısını getir.
     * English: Get subscription count.
     */
    @Query("SELECT COUNT(*) FROM subscriptions")
    suspend fun getSubscriptionCount(): Int

    /**
     * Turkish: Aktif abonelik sayısını getir.
     * English: Get active subscription count.
     */
    @Query("SELECT COUNT(*) FROM subscriptions WHERE status = 'ACTIVE'")
    suspend fun getActiveSubscriptionCount(): Int

    /**
     * Turkish: İptal edilmiş abonelik sayısını getir.
     * English: Get cancelled subscription count.
     */
    @Query("SELECT COUNT(*) FROM subscriptions WHERE status = 'CANCELLED'")
    suspend fun getCancelledSubscriptionCount(): Int

    /**
     * Turkish: Abonelik güncelle.
     * English: Update subscription.
     */
    @Update
    suspend fun updateSubscription(subscription: SubscriptionEntity)

    /**
     * Turkish: Abonelik sil.
     * English: Delete subscription.
     */
    @Delete
    suspend fun deleteSubscription(subscription: SubscriptionEntity)

    /**
     * Turkish: Servis adına göre abonelik sil.
     * English: Delete subscription by service name.
     */
    @Query("DELETE FROM subscriptions WHERE serviceName = :serviceName")
    suspend fun deleteSubscriptionByServiceName(serviceName: String)

    /**
     * Turkish: Tüm abonelikleri sil.
     * English: Delete all subscriptions.
     */
    @Query("DELETE FROM subscriptions")
    suspend fun deleteAllSubscriptions()
}
