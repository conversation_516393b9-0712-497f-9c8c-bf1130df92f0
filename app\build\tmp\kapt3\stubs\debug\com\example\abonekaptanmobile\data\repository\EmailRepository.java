package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import com.example.abonekaptanmobile.data.local.entity.EmailEntity;
import com.example.abonekaptanmobile.model.RawEmail;
import kotlinx.coroutines.flow.Flow;
import javax.inject.Inject;
import javax.inject.Singleton;

/**
 * Turkish: E-posta veritabanı işlemleri için repository.
 * English: Repository for email database operations.
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u000e\u0010\u0005\u001a\u00020\u0006H\u0086@\u00a2\u0006\u0002\u0010\u0007J\u0012\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000b0\n0\tJ\u000e\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\u0007J\u001c\u0010\u000e\u001a\u00020\u00062\f\u0010\u000f\u001a\b\u0012\u0004\u0012\u00020\u00100\nH\u0086@\u00a2\u0006\u0002\u0010\u0011R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0012"}, d2 = {"Lcom/example/abonekaptanmobile/data/repository/EmailRepository;", "", "emailDao", "Lcom/example/abonekaptanmobile/data/local/dao/EmailDao;", "(Lcom/example/abonekaptanmobile/data/local/dao/EmailDao;)V", "deleteAllEmails", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllEmails", "Lkotlinx/coroutines/flow/Flow;", "", "Lcom/example/abonekaptanmobile/data/local/entity/EmailEntity;", "getEmailCount", "", "insertEmails", "emails", "Lcom/example/abonekaptanmobile/model/RawEmail;", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class EmailRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.local.dao.EmailDao emailDao = null;
    
    @javax.inject.Inject()
    public EmailRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.EmailDao emailDao) {
        super();
    }
    
    /**
     * Turkish: E-postaları veritabanına kaydet.
     * English: Insert emails into database.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertEmails(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.model.RawEmail> emails, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    /**
     * Turkish: Tüm e-postaları getir.
     * English: Get all emails.
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.EmailEntity>> getAllEmails() {
        return null;
    }
    
    /**
     * Turkish: E-posta sayısını getir.
     * English: Get email count.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getEmailCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion) {
        return null;
    }
    
    /**
     * Turkish: Tüm e-postaları sil.
     * English: Delete all emails.
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object deleteAllEmails(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}