package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.FeedbackDao;
import com.example.abonekaptanmobile.data.local.entity.FeedbackEntity;
import javax.inject.Inject;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0002\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006H\u0086@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\u0007H\u0086@\u00a2\u0006\u0002\u0010\fJ\u0016\u0010\r\u001a\u00020\n2\u0006\u0010\u000e\u001a\u00020\u000fH\u0086@\u00a2\u0006\u0002\u0010\u0010R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0011"}, d2 = {"Lcom/example/abonekaptanmobile/data/repository/FeedbackRepository;", "", "dao", "Lcom/example/abonekaptanmobile/data/local/dao/FeedbackDao;", "(Lcom/example/abonekaptanmobile/data/local/dao/FeedbackDao;)V", "getPendingFeedback", "", "Lcom/example/abonekaptanmobile/data/local/entity/FeedbackEntity;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertFeedback", "", "feedback", "(Lcom/example/abonekaptanmobile/data/local/entity/FeedbackEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "markFeedbackAsProcessed", "feedbackId", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_release"})
public final class FeedbackRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.abonekaptanmobile.data.local.dao.FeedbackDao dao = null;
    
    @javax.inject.Inject()
    public FeedbackRepository(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.dao.FeedbackDao dao) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object insertFeedback(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.FeedbackEntity feedback, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getPendingFeedback(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.abonekaptanmobile.data.local.entity.FeedbackEntity>> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object markFeedbackAsProcessed(long feedbackId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}