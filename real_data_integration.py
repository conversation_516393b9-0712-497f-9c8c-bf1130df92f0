#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gerçek Android Veritabanı Entegrasyonu
Real Android Database Integration
"""

import sqlite3
import os
import shutil
from datetime import datetime
import time

class RealDataIntegration:
    """Android uygulamasından gerçek verileri çeken sınıf"""
    
    def __init__(self):
        # Android veritabanı yolları
        self.android_db_paths = [
            # Emülatör yolu
            r"C:\Users\<USER>\.android\avd\{emulator_name}.avd\data\data\com.example.abonekaptanmobile\databases\abone_kaptan_db",
            # Gerçek cihaz yolu (root gerekli)
            "/data/data/com.example.abonekaptanmobile/databases/abone_kaptan_db",
            # ADB pull ile çekilen dosya
            "./android_abone_kaptan_db.sqlite"
        ]
        
        self.local_db_path = "abone_kaptan_db.sqlite"
        self.backup_dir = "db_backups"
        
    def find_android_database(self):
        """Android veritabanı dosyasını bul"""
        for path in self.android_db_paths:
            if os.path.exists(path):
                print(f"✅ Android veritabanı bulundu: {path}")
                return path
        
        print("❌ Android veritabanı bulunamadı!")
        print("📱 Android uygulamasını çalıştırın ve email analizi yapın")
        return None
    
    def backup_current_db(self):
        """Mevcut veritabanını yedekle"""
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        if os.path.exists(self.local_db_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = os.path.join(self.backup_dir, f"backup_{timestamp}.sqlite")
            shutil.copy2(self.local_db_path, backup_path)
            print(f"💾 Mevcut veritabanı yedeklendi: {backup_path}")
    
    def sync_from_android(self):
        """Android'den gerçek verileri senkronize et"""
        android_db = self.find_android_database()
        if not android_db:
            return False
        
        try:
            # Mevcut veritabanını yedekle
            self.backup_current_db()
            
            # Android veritabanını kopyala
            shutil.copy2(android_db, self.local_db_path)
            print(f"📱➡️💻 Android veritabanı kopyalandı")
            
            # Veritabanını kontrol et
            return self.verify_database()
            
        except Exception as e:
            print(f"❌ Senkronizasyon hatası: {e}")
            return False
    
    def verify_database(self):
        """Veritabanını doğrula"""
        try:
            conn = sqlite3.connect(self.local_db_path)
            cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis")
            count = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1")
            yes_count = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM stage_analysis WHERE stage1_isSubscriptionCompany = 0")
            no_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ Veritabanı doğrulandı:")
            print(f"   📊 Toplam kayıt: {count}")
            print(f"   ✅ EVET: {yes_count}")
            print(f"   ❌ HAYIR: {no_count}")
            
            return count > 0
            
        except Exception as e:
            print(f"❌ Veritabanı doğrulama hatası: {e}")
            return False
    
    def watch_for_changes(self, interval=30):
        """Android veritabanındaki değişiklikleri izle"""
        android_db = self.find_android_database()
        if not android_db:
            return
        
        print(f"👀 Android veritabanı izleniyor... (Her {interval} saniyede kontrol)")
        print("⏹️  Durdurmak için Ctrl+C")
        
        last_modified = 0
        
        try:
            while True:
                if os.path.exists(android_db):
                    current_modified = os.path.getmtime(android_db)
                    
                    if current_modified > last_modified:
                        print(f"🔄 Değişiklik tespit edildi: {datetime.now().strftime('%H:%M:%S')}")
                        if self.sync_from_android():
                            print("✅ Senkronizasyon tamamlandı")
                        last_modified = current_modified
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n⏹️  İzleme durduruldu")

def pull_from_android_device():
    """ADB ile Android cihazından veritabanını çek"""
    print("📱 Android cihazından veritabanı çekiliyor...")
    
    # ADB komutları
    commands = [
        "adb shell su -c 'cp /data/data/com.example.abonekaptanmobile/databases/abone_kaptan_db /sdcard/'",
        "adb pull /sdcard/abone_kaptan_db ./android_abone_kaptan_db.sqlite",
        "adb shell rm /sdcard/abone_kaptan_db"
    ]
    
    for cmd in commands:
        print(f"🔧 Çalıştırılıyor: {cmd}")
        result = os.system(cmd)
        if result != 0:
            print(f"❌ Komut başarısız: {cmd}")
            return False
    
    print("✅ Android veritabanı başarıyla çekildi")
    return True

def main():
    """Ana fonksiyon"""
    print("🔄 Gerçek Android Veritabanı Entegrasyonu")
    print("=" * 50)
    
    integration = RealDataIntegration()
    
    print("\n📋 Seçenekler:")
    print("1. Android veritabanını bul ve senkronize et")
    print("2. ADB ile cihazdan veritabanını çek")
    print("3. Değişiklikleri sürekli izle")
    print("4. Mevcut veritabanını doğrula")
    
    choice = input("\n🔢 Seçiminizi yapın (1-4): ").strip()
    
    if choice == "1":
        if integration.sync_from_android():
            print("🎉 Senkronizasyon başarılı!")
        else:
            print("❌ Senkronizasyon başarısız!")
    
    elif choice == "2":
        if pull_from_android_device():
            integration = RealDataIntegration()
            if integration.sync_from_android():
                print("🎉 ADB ile senkronizasyon başarılı!")
    
    elif choice == "3":
        interval = input("⏱️  Kontrol aralığı (saniye, varsayılan 30): ").strip()
        interval = int(interval) if interval.isdigit() else 30
        integration.watch_for_changes(interval)
    
    elif choice == "4":
        integration.verify_database()
    
    else:
        print("❌ Geçersiz seçim!")

if __name__ == "__main__":
    main()
