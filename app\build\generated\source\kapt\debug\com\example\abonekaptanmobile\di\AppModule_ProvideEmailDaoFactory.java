// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.di;

import com.example.abonekaptanmobile.data.local.AppDatabase;
import com.example.abonekaptanmobile.data.local.dao.EmailDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class AppModule_ProvideEmailDaoFactory implements Factory<EmailDao> {
  private final Provider<AppDatabase> appDatabaseProvider;

  public AppModule_ProvideEmailDaoFactory(Provider<AppDatabase> appDatabaseProvider) {
    this.appDatabaseProvider = appDatabaseProvider;
  }

  @Override
  public EmailDao get() {
    return provideEmailDao(appDatabaseProvider.get());
  }

  public static AppModule_ProvideEmailDaoFactory create(Provider<AppDatabase> appDatabaseProvider) {
    return new AppModule_ProvideEmailDaoFactory(appDatabaseProvider);
  }

  public static EmailDao provideEmailDao(AppDatabase appDatabase) {
    return Preconditions.checkNotNullFromProvides(AppModule.INSTANCE.provideEmailDao(appDatabase));
  }
}
