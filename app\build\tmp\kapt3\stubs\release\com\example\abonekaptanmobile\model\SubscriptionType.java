package com.example.abonekaptanmobile.model;

import com.example.abonekaptanmobile.data.remote.model.ClassificationResult;

/**
 * Turkish: Abonelik türlerini temsil eden enum.
 * English: Enum representing subscription types.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0007\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007\u00a8\u0006\b"}, d2 = {"Lcom/example/abonekaptanmobile/model/SubscriptionType;", "", "(Ljava/lang/String;I)V", "PAID", "FREE", "PROMOTIONAL", "NOT_SUBSCRIPTION", "UNKNOWN", "app_release"})
public enum SubscriptionType {
    /*public static final*/ PAID /* = new PAID() */,
    /*public static final*/ FREE /* = new FREE() */,
    /*public static final*/ PROMOTIONAL /* = new PROMOTIONAL() */,
    /*public static final*/ NOT_SUBSCRIPTION /* = new NOT_SUBSCRIPTION() */,
    /*public static final*/ UNKNOWN /* = new UNKNOWN() */;
    
    SubscriptionType() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.abonekaptanmobile.model.SubscriptionType> getEntries() {
        return null;
    }
}