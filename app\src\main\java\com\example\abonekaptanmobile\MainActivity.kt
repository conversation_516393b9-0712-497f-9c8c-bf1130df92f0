package com.example.abonekaptanmobile

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.example.abonekaptanmobile.ui.screens.SignInScreen
import com.example.abonekaptanmobile.ui.screens.StageAnalysisScreen
import com.example.abonekaptanmobile.ui.screens.SubscriptionListScreen
import com.example.abonekaptanmobile.ui.theme.AboneKaptanMobileTheme
import com.example.abonekaptanmobile.ui.viewmodel.MainViewModel
import com.google.android.gms.auth.api.signin.GoogleSignIn
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            AboneKaptanMobileTheme {
                val viewModel: MainViewModel = hiltViewModel()
                val isSignedIn by viewModel.isSignedIn.collectAsState()
                val isLoading by viewModel.isLoading.collectAsState()
                val isSigningIn by viewModel.isSigningIn.collectAsState()
                val error by viewModel.error.collectAsState()
                val emailProcessingProgress by viewModel.emailProcessingProgress.collectAsState()
                val emailProcessingStatus by viewModel.emailProcessingStatus.collectAsState()
                var currentScreen by remember { mutableStateOf("subscriptions") }
                val snackbarHostState = remember { SnackbarHostState() }


                val signInLauncher = rememberLauncherForActivityResult(
                    contract = ActivityResultContracts.StartActivityForResult()
                ) { result ->
                    val task = GoogleSignIn.getSignedInAccountFromIntent(result.data)
                    viewModel.handleSignInResult(task)
                }

                LaunchedEffect(error) {
                    error?.let {
                        snackbarHostState.showSnackbar(
                            message = it,
                            duration = SnackbarDuration.Long
                        )
                        viewModel.clearError() // Hata gösterildikten sonra temizle
                    }
                }

                Scaffold(
                    snackbarHost = { SnackbarHost(snackbarHostState) },
                    topBar = {
                        TopAppBar(
                            title = {
                                Text(
                                    when (currentScreen) {
                                        "analysis" -> "Analiz Detayları"
                                        else -> stringResource(id = R.string.app_name)
                                    }
                                )
                            },
                            navigationIcon = {
                                if (currentScreen == "analysis") {
                                    IconButton(onClick = { currentScreen = "subscriptions" }) {
                                        Icon(Icons.Filled.ArrowBack, contentDescription = "Geri")
                                    }
                                }
                            },
                            colors = TopAppBarDefaults.topAppBarColors(
                                containerColor = MaterialTheme.colorScheme.primaryContainer,
                                titleContentColor = MaterialTheme.colorScheme.onPrimaryContainer
                            )
                        )
                    }
                ) { paddingValues ->
                    Surface(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(paddingValues),
                        color = MaterialTheme.colorScheme.background
                    ) {
                        if (isLoading || isSigningIn) {
                            Box(
                                contentAlignment = Alignment.Center,
                                modifier = Modifier.fillMaxSize()
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.Center
                                ) {
                                    if (isSigningIn) {
                                        CircularProgressIndicator()
                                        Spacer(modifier = Modifier.height(16.dp))
                                        Text("Giriş yapılıyor...")
                                    } else if (isLoading) {
                                        // Email processing progress
                                        if (emailProcessingProgress > 0f) {
                                            LinearProgressIndicator(
                                                progress = emailProcessingProgress,
                                                modifier = Modifier
                                                    .fillMaxWidth(0.8f)
                                                    .height(8.dp)
                                            )
                                            Spacer(modifier = Modifier.height(16.dp))
                                            Text(
                                                text = emailProcessingStatus ?: "E-postalar işleniyor...",
                                                style = MaterialTheme.typography.bodyMedium
                                            )
                                            Spacer(modifier = Modifier.height(8.dp))
                                            Text(
                                                text = "${(emailProcessingProgress * 100).toInt()}% tamamlandı",
                                                style = MaterialTheme.typography.bodySmall,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant
                                            )
                                        } else {
                                            CircularProgressIndicator()
                                            Spacer(modifier = Modifier.height(16.dp))
                                            Text("E-postalar yükleniyor...")
                                        }
                                    }
                                }
                            }
                        } else {
                            if (isSignedIn) {
                                when (currentScreen) {
                                    "subscriptions" -> SubscriptionListScreen(
                                        viewModel = viewModel,
                                        onNavigateToAnalysis = { currentScreen = "analysis" }
                                    )
                                    "analysis" -> StageAnalysisScreen()
                                }
                            } else {
                                SignInScreen(
                                    onSignInClick = {
                                        viewModel.startSignIn(signInLauncher)
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}