package com.example.abonekaptanmobile.data.remote.model;

import com.google.gson.annotations.SerializedName;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0019\n\u0002\u0010\u000b\n\u0002\b\u0004\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u000e\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\b\u001a\u0004\u0018\u00010\t\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u000b\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\f\u001a\u0004\u0018\u00010\r\u00a2\u0006\u0002\u0010\u000eJ\t\u0010\u001c\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u001d\u001a\u00020\u0003H\u00c6\u0003J\u0011\u0010\u001e\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u0006H\u00c6\u0003J\u000b\u0010\u001f\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010 \u001a\u0004\u0018\u00010\tH\u00c6\u0003J\u000b\u0010!\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010\"\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u0010\u0010#\u001a\u0004\u0018\u00010\rH\u00c6\u0003\u00a2\u0006\u0002\u0010\u0018Jp\u0010$\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\u0010\b\u0002\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\b\u001a\u0004\u0018\u00010\t2\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u000b\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\rH\u00c6\u0001\u00a2\u0006\u0002\u0010%J\u0013\u0010&\u001a\u00020\'2\b\u0010(\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010)\u001a\u00020\rH\u00d6\u0001J\t\u0010*\u001a\u00020\u0003H\u00d6\u0001R\u0018\u0010\u000b\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\u0010R\u0016\u0010\u0002\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0011\u0010\u0010R\u0018\u0010\n\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0010R\u001e\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0003\u0018\u00010\u00068\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0013\u0010\u0014R\u0018\u0010\b\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0015\u0010\u0016R\u001a\u0010\f\u001a\u0004\u0018\u00010\r8\u0006X\u0087\u0004\u00a2\u0006\n\n\u0002\u0010\u0019\u001a\u0004\b\u0017\u0010\u0018R\u0018\u0010\u0007\u001a\u0004\u0018\u00010\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0010R\u0016\u0010\u0004\u001a\u00020\u00038\u0006X\u0087\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001b\u0010\u0010\u00a8\u0006+"}, d2 = {"Lcom/example/abonekaptanmobile/data/remote/model/GmailMessage;", "", "id", "", "threadId", "labelIds", "", "snippet", "payload", "Lcom/example/abonekaptanmobile/data/remote/model/MessagePayload;", "internalDate", "historyId", "sizeEstimate", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lcom/example/abonekaptanmobile/data/remote/model/MessagePayload;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)V", "getHistoryId", "()Ljava/lang/String;", "getId", "getInternalDate", "getLabelIds", "()Ljava/util/List;", "getPayload", "()Lcom/example/abonekaptanmobile/data/remote/model/MessagePayload;", "getSizeEstimate", "()Ljava/lang/Integer;", "Ljava/lang/Integer;", "getSnippet", "getThreadId", "component1", "component2", "component3", "component4", "component5", "component6", "component7", "component8", "copy", "(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/lang/String;Lcom/example/abonekaptanmobile/data/remote/model/MessagePayload;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Lcom/example/abonekaptanmobile/data/remote/model/GmailMessage;", "equals", "", "other", "hashCode", "toString", "app_release"})
public final class GmailMessage {
    @com.google.gson.annotations.SerializedName(value = "id")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @com.google.gson.annotations.SerializedName(value = "threadId")
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String threadId = null;
    @com.google.gson.annotations.SerializedName(value = "labelIds")
    @org.jetbrains.annotations.Nullable()
    private final java.util.List<java.lang.String> labelIds = null;
    @com.google.gson.annotations.SerializedName(value = "snippet")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String snippet = null;
    @com.google.gson.annotations.SerializedName(value = "payload")
    @org.jetbrains.annotations.Nullable()
    private final com.example.abonekaptanmobile.data.remote.model.MessagePayload payload = null;
    @com.google.gson.annotations.SerializedName(value = "internalDate")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String internalDate = null;
    @com.google.gson.annotations.SerializedName(value = "historyId")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String historyId = null;
    @com.google.gson.annotations.SerializedName(value = "sizeEstimate")
    @org.jetbrains.annotations.Nullable()
    private final java.lang.Integer sizeEstimate = null;
    
    public GmailMessage(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String threadId, @org.jetbrains.annotations.Nullable()
    java.util.List<java.lang.String> labelIds, @org.jetbrains.annotations.Nullable()
    java.lang.String snippet, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.MessagePayload payload, @org.jetbrains.annotations.Nullable()
    java.lang.String internalDate, @org.jetbrains.annotations.Nullable()
    java.lang.String historyId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer sizeEstimate) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getThreadId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<java.lang.String> getLabelIds() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getSnippet() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.MessagePayload getPayload() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getInternalDate() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getHistoryId() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer getSizeEstimate() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.util.List<java.lang.String> component3() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component4() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.abonekaptanmobile.data.remote.model.MessagePayload component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Integer component8() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.abonekaptanmobile.data.remote.model.GmailMessage copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String threadId, @org.jetbrains.annotations.Nullable()
    java.util.List<java.lang.String> labelIds, @org.jetbrains.annotations.Nullable()
    java.lang.String snippet, @org.jetbrains.annotations.Nullable()
    com.example.abonekaptanmobile.data.remote.model.MessagePayload payload, @org.jetbrains.annotations.Nullable()
    java.lang.String internalDate, @org.jetbrains.annotations.Nullable()
    java.lang.String historyId, @org.jetbrains.annotations.Nullable()
    java.lang.Integer sizeEstimate) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}