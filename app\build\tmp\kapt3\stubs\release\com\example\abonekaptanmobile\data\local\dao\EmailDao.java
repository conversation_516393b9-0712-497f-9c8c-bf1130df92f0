package com.example.abonekaptanmobile.data.local.dao;

import androidx.room.*;
import com.example.abonekaptanmobile.data.local.entity.EmailEntity;
import kotlinx.coroutines.flow.Flow;

/**
 * Turkish: E-posta veritabanı eri<PERSON>im <PERSON>.
 * English: Email database access object.
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000>\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u000b\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\u0005\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u0014\u0010\r\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000eH\'J\u0018\u0010\u0010\u001a\u0004\u0018\u00010\u00072\u0006\u0010\n\u001a\u00020\u000bH\u00a7@\u00a2\u0006\u0002\u0010\fJ\u000e\u0010\u0011\u001a\u00020\u0012H\u00a7@\u00a2\u0006\u0002\u0010\u0004J$\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0015H\'J\u001c\u0010\u0017\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u0018\u001a\u00020\u000bH\'J\u001c\u0010\u0019\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00070\u000f0\u000e2\u0006\u0010\u001a\u001a\u00020\u000bH\'J\u0016\u0010\u001b\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\bJ\u001c\u0010\u001c\u001a\u00020\u00032\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00070\u000fH\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010\u001f\u001a\u00020\u00032\u0006\u0010\u0006\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\b\u00a8\u0006 "}, d2 = {"Lcom/example/abonekaptanmobile/data/local/dao/EmailDao;", "", "deleteAllEmails", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteEmail", "email", "Lcom/example/abonekaptanmobile/data/local/entity/EmailEntity;", "(Lcom/example/abonekaptanmobile/data/local/entity/EmailEntity;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteEmailById", "emailId", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllEmails", "Lkotlinx/coroutines/flow/Flow;", "", "getEmailById", "getEmailCount", "", "getEmailsByDateRange", "startDate", "", "endDate", "getEmailsBySender", "senderPattern", "getEmailsBySubject", "subjectPattern", "insertEmail", "insertEmails", "emails", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateEmail", "app_release"})
@androidx.room.Dao()
public abstract interface EmailDao {
    
    /**
     * Turkish: E-postaları veritabanına ekle.
     * English: Insert emails into database.
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertEmails(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.abonekaptanmobile.data.local.entity.EmailEntity> emails, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tek e-posta ekle.
     * English: Insert single email.
     */
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertEmail(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.EmailEntity email, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tüm e-postaları getir.
     * English: Get all emails.
     */
    @androidx.room.Query(value = "SELECT * FROM emails ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.EmailEntity>> getAllEmails();
    
    /**
     * Turkish: E-posta ID'sine göre getir.
     * English: Get email by ID.
     */
    @androidx.room.Query(value = "SELECT * FROM emails WHERE id = :emailId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmailById(@org.jetbrains.annotations.NotNull()
    java.lang.String emailId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.abonekaptanmobile.data.local.entity.EmailEntity> $completion);
    
    /**
     * Turkish: Gönderen adresine göre e-postaları getir.
     * English: Get emails by sender address.
     */
    @androidx.room.Query(value = "SELECT * FROM emails WHERE `from` LIKE :senderPattern ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.EmailEntity>> getEmailsBySender(@org.jetbrains.annotations.NotNull()
    java.lang.String senderPattern);
    
    /**
     * Turkish: Konu başlığına göre e-postaları getir.
     * English: Get emails by subject.
     */
    @androidx.room.Query(value = "SELECT * FROM emails WHERE subject LIKE :subjectPattern ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.EmailEntity>> getEmailsBySubject(@org.jetbrains.annotations.NotNull()
    java.lang.String subjectPattern);
    
    /**
     * Turkish: Tarih aralığına göre e-postaları getir.
     * English: Get emails by date range.
     */
    @androidx.room.Query(value = "SELECT * FROM emails WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.abonekaptanmobile.data.local.entity.EmailEntity>> getEmailsByDateRange(long startDate, long endDate);
    
    /**
     * Turkish: E-posta sayısını getir.
     * English: Get email count.
     */
    @androidx.room.Query(value = "SELECT COUNT(*) FROM emails")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getEmailCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * Turkish: E-posta güncelle.
     * English: Update email.
     */
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateEmail(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.EmailEntity email, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: E-posta sil.
     * English: Delete email.
     */
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEmail(@org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.data.local.entity.EmailEntity email, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: E-posta ID'sine göre sil.
     * English: Delete email by ID.
     */
    @androidx.room.Query(value = "DELETE FROM emails WHERE id = :emailId")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteEmailById(@org.jetbrains.annotations.NotNull()
    java.lang.String emailId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    /**
     * Turkish: Tüm e-postaları sil.
     * English: Delete all emails.
     */
    @androidx.room.Query(value = "DELETE FROM emails")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllEmails(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}