<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aşama 1 Email Analiz <PERSON>uçları</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .result-yes { background-color: #d4edda !important; }
        .result-no { background-color: #f8d7da !important; }
        .confidence-high { color: #198754; font-weight: bold; }
        .confidence-medium { color: #fd7e14; font-weight: bold; }
        .confidence-low { color: #dc3545; font-weight: bold; }
        .table-container { max-height: 70vh; overflow-y: auto; }
        .stats-card { border-left: 4px solid #0d6efd; }
        .domain-badge { font-size: 0.8em; }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center mb-3">
                    <i class="fas fa-envelope-open-text text-primary"></i>
                    Aşama 1 Email Analiz Sonuçları
                </h1>
                <p class="text-center text-muted">
                    Email'lerin abonelik şirketi olup olmadığının analiz sonuçları
                </p>
            </div>
        </div>

        <!-- İstatistikler -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-list-ol"></i> Toplam
                        </h5>
                        <h2 class="text-primary">{{ stats['total'] or 0 }}</h2>
                        <small class="text-muted">Analiz edilen email</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100" style="border-left: 4px solid #198754;">
                    <div class="card-body text-center">
                        <h5 class="card-title text-success">
                            <i class="fas fa-check-circle"></i> EVET
                        </h5>
                        <h2 class="text-success">{{ stats['yes_count'] or 0 }}</h2>
                        <small class="text-muted">
                            {% if stats['total'] and stats['total'] > 0 %}
                                {{ "%.1f"|format((stats['yes_count'] or 0) / stats['total'] * 100) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100" style="border-left: 4px solid #dc3545;">
                    <div class="card-body text-center">
                        <h5 class="card-title text-danger">
                            <i class="fas fa-times-circle"></i> HAYIR
                        </h5>
                        <h2 class="text-danger">{{ stats['no_count'] or 0 }}</h2>
                        <small class="text-muted">
                            {% if stats['total'] and stats['total'] > 0 %}
                                {{ "%.1f"|format((stats['no_count'] or 0) / stats['total'] * 100) }}%
                            {% else %}
                                0%
                            {% endif %}
                        </small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card h-100" style="border-left: 4px solid #fd7e14;">
                    <div class="card-body text-center">
                        <h5 class="card-title text-warning">
                            <i class="fas fa-chart-line"></i> Ortalama Güven
                        </h5>
                        <h2 class="text-warning">{{ "%.2f"|format(stats['avg_confidence'] or 0) }}</h2>
                        <small class="text-muted">Güven skoru</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtreler -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="filter" class="form-label">
                                    <i class="fas fa-filter"></i> Sonuç Filtresi
                                </label>
                                <select name="filter" id="filter" class="form-select">
                                    <option value="all" {% if filter_result == 'all' %}selected{% endif %}>
                                        Tümü
                                    </option>
                                    <option value="yes" {% if filter_result == 'yes' %}selected{% endif %}>
                                        Sadece EVET
                                    </option>
                                    <option value="no" {% if filter_result == 'no' %}selected{% endif %}>
                                        Sadece HAYIR
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="search" class="form-label">
                                    <i class="fas fa-search"></i> Arama
                                </label>
                                <input type="text" name="search" id="search" class="form-control" 
                                       value="{{ search_query }}" 
                                       placeholder="Domain, konu, şirket adı veya gönderen ara...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filtrele
                                    </button>
                                    <a href="/export/csv?filter={{ filter_result }}&search={{ search_query }}"
                                       class="btn btn-success btn-sm">
                                        <i class="fas fa-download"></i> CSV İndir
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sonuç Sayısı -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>{{ total_results }}</strong> sonuç gösteriliyor
                    {% if filter_result != 'all' or search_query %}
                        (filtrelenmiş)
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Sonuçlar Tablosu -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table"></i> Analiz Sonuçları
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-container">
                            <table class="table table-striped table-hover mb-0">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        <th>#</th>
                                        <th>Sonuç</th>
                                        <th>Domain</th>
                                        <th>Şirket Adı</th>
                                        <th>Güven</th>
                                        <th>Konu</th>
                                        <th>Gönderen</th>
                                        <th>Tarih</th>
                                        <th>Durum</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for result in results %}
                                    <tr class="{% if result.stage1_isSubscriptionCompany %}result-yes{% else %}result-no{% endif %}">
                                        <td>{{ result.emailIndex }}</td>
                                        <td>
                                            <span class="badge bg-{{ result.result_class }} fs-6">
                                                {% if result.stage1_isSubscriptionCompany %}
                                                    <i class="fas fa-check"></i> EVET
                                                {% else %}
                                                    <i class="fas fa-times"></i> HAYIR
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary domain-badge">{{ result.domain }}</span>
                                        </td>
                                        <td>
                                            <strong>{{ result.stage1_companyName }}</strong>
                                        </td>
                                        <td>
                                            <span class="{% if result.stage1_companyConfidence >= 0.8 %}confidence-high{% elif result.stage1_companyConfidence >= 0.5 %}confidence-medium{% else %}confidence-low{% endif %}">
                                                {{ result.stage1_companyConfidence }}
                                            </span>
                                        </td>
                                        <td>
                                            <small title="{{ result.subject }}">{{ result.subject }}</small>
                                        </td>
                                        <td>
                                            <small>{{ result.from[:50] }}{% if result.from|length > 50 %}...{% endif %}</small>
                                        </td>
                                        <td>
                                            <small>{{ result.date }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ result.processingStatus }}</span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="row mt-4">
            <div class="col-12 text-center">
                <small class="text-muted">
                    <i class="fas fa-clock"></i>
                    Aşama 1 Email Analiz Sonuçları - Demo Veritabanı
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let lastDbTimestamp = 0;

        // Veritabanı değişikliklerini kontrol et
        function checkDatabaseChanges() {
            fetch('/api/db_status')
                .then(response => response.json())
                .then(data => {
                    if (data.exists && data.db_changed && data.timestamp > lastDbTimestamp) {
                        console.log('Veritabanı değişikliği tespit edildi, sayfa yenileniyor...');
                        showUpdateNotification();
                        setTimeout(() => location.reload(), 2000);
                    }
                    lastDbTimestamp = data.timestamp || 0;
                })
                .catch(error => console.log('DB status check error:', error));
        }

        // Güncelleme bildirimi göster
        function showUpdateNotification() {
            const notification = document.createElement('div');
            notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-sync-alt fa-spin"></i>
                <strong>Yeni veriler tespit edildi!</strong><br>
                Sayfa 2 saniye içinde otomatik yenilenecek...
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(notification);
        }

        // Her 10 saniyede veritabanı değişikliklerini kontrol et
        setInterval(checkDatabaseChanges, 10000);

        // Sayfa yüklendiğinde ilk kontrol
        checkDatabaseChanges();

        // Fallback: 5 dakikada bir tam yenileme
        setTimeout(function() {
            console.log('5 dakikalık fallback yenileme');
            location.reload();
        }, 300000);
    </script>
</body>
</html>
