{"logs": [{"outputFile": "com.example.abonekaptanmobile.app-mergeReleaseResources-63:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\fd203b27384ed6abbecfa6eb1dbc818e\\transformed\\navigation-common-2.5.1\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "424,437,443,449,458", "startColumns": "4,4,4,4,4", "startOffsets": "24713,25352,25596,25843,26206", "endLines": "436,442,448,451,462", "endColumns": "24,24,24,24,24", "endOffsets": "25347,25591,25838,25971,26383"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\44e19e1f9b56976b7f9e33316bf0ba7e\\transformed\\play-services-base-18.0.1\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "12,13,14,15,16,17,18,19,137,138,139,140,141,142,143,144,146,147,148,149,150,151,152,153,154,411,463", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "803,893,973,1063,1153,1233,1314,1394,8504,8609,8790,8915,9022,9202,9325,9441,9711,9899,10004,10185,10310,10485,10633,10696,10758,24398,26388", "endLines": "12,13,14,15,16,17,18,19,137,138,139,140,141,142,143,144,146,147,148,149,150,151,152,153,154,423,481", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "888,968,1058,1148,1228,1309,1389,1469,8604,8785,8910,9017,9197,9320,9436,9539,9894,9999,10180,10305,10480,10628,10691,10753,10832,24708,26800"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae9863232b9716e1e6ec45d64efe02de\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "258,259", "startColumns": "4,4", "startOffsets": "18339,18395", "endColumns": "55,54", "endOffsets": "18390,18445"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\a9c34032e693f558d3bfaaeb1f82e9ed\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "88,97,118,363,368", "startColumns": "4,4,4,4,4", "startOffsets": "5655,6104,7235,23230,23400", "endLines": "88,97,118,367,371", "endColumns": "56,64,63,24,24", "endOffsets": "5707,6164,7294,23395,23544"}}, {"source": "D:\\abonelik_sistemi_final (3)\\abonelik_sistemi_final (2)\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "7,1,25,13,9,16,11,27,15,23,20,18,19,21,22,28,8,14,12,26,5,4,2,3,6,24,10,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "420,16,1721,834,571,1023,716,1900,949,1599,1363,1172,1264,1446,1531,1993,487,893,771,1762,299,239,72,170,369,1670,649,1088", "endColumns": "66,55,40,58,77,64,54,92,73,70,82,91,98,84,67,76,83,55,62,137,69,59,97,68,50,50,66,83", "endOffsets": "482,67,1757,888,644,1083,766,1988,1018,1665,1441,1259,1358,1526,1594,2065,566,944,829,1895,364,294,165,234,415,1716,711,1167"}, "to": {"startLines": "122,124,132,133,134,155,159,160,161,162,163,164,165,166,167,168,169,177,178,240,247,249,250,251,252,254,260,261", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7490,7640,8207,8248,8307,10837,11127,11182,11275,11349,11420,11503,11595,11694,11779,11847,11924,12651,12707,17199,17650,17766,17826,17924,17993,18115,18450,18517", "endColumns": "66,55,40,58,77,64,54,92,73,70,82,91,98,84,67,76,83,55,62,137,69,59,97,68,50,50,66,83", "endOffsets": "7552,7691,8243,8302,8380,10897,11177,11270,11344,11415,11498,11590,11689,11774,11842,11919,12003,12702,12765,17332,17715,17821,17919,17988,18039,18161,18512,18596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1062e727b039c203a5f49af9a364889\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "179,180,181,182,183,184,185,186,187,188,191,192,193,194,195,196,197,198,199,200,201,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12770,12858,12944,13025,13109,13178,13243,13326,13432,13518,13638,13692,13761,13822,13891,13980,14075,14149,14246,14339,14437,14586,14677,14765,14861,14959,15023,15091,15178,15272,15339,15411,15483,15584,15693,15769,15838,15886,15952,16016,16073,16130,16202,16252,16306,16377,16448,16518,16587,16645,16721,16792,16866,16952,17002,17072", "endLines": "179,180,181,182,183,184,185,186,187,190,191,192,193,194,195,196,197,198,199,200,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "12853,12939,13020,13104,13173,13238,13321,13427,13513,13633,13687,13756,13817,13886,13975,14070,14144,14241,14334,14432,14581,14672,14760,14856,14954,15018,15086,15173,15267,15334,15406,15478,15579,15688,15764,15833,15881,15947,16011,16068,16125,16197,16247,16301,16372,16443,16513,16582,16640,16716,16787,16861,16947,16997,17067,17132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\736535d916a2f202cc5de9983b95bb18\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "91,95", "startColumns": "4,4", "startOffsets": "5818,5995", "endColumns": "53,66", "endOffsets": "5867,6057"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\058afe60a62462f8a077b2f93aad827c\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "117", "startColumns": "4", "startOffsets": "7185", "endColumns": "49", "endOffsets": "7230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7ac341cec7d42871491bf80bed891004\\transformed\\work-runtime-2.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,120,190,254", "endColumns": "64,69,63,60", "endOffsets": "115,185,249,310"}, "to": {"startLines": "3,4,5,6", "startColumns": "4,4,4,4", "startOffsets": "210,275,345,409", "endColumns": "64,69,63,60", "endOffsets": "270,340,404,465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\3e9e2d1cf43cde8a32196cdda01a6068\\transformed\\navigation-runtime-2.5.1\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "94,286,452,455", "startColumns": "4,4,4,4", "startOffsets": "5942,20010,25976,26091", "endLines": "94,292,454,457", "endColumns": "52,24,24,24", "endOffsets": "5990,20309,26086,26201"}}, {"source": "D:\\abonelik_sistemi_final (3)\\abonelik_sistemi_final (2)\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "94", "endOffsets": "146"}, "to": {"startLines": "283", "startColumns": "4", "startOffsets": "19777", "endColumns": "93", "endOffsets": "19866"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\67663ff03f7437bfd40a417d66c88a5b\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "116", "startColumns": "4", "startOffsets": "7131", "endColumns": "53", "endOffsets": "7180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\5ac9d68b92fe2ab1028cb5e8476ae3bd\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "96,115", "startColumns": "4,4", "startOffsets": "6062,7071", "endColumns": "41,59", "endOffsets": "6099,7126"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f17758470c37236a8702f22be0817404\\transformed\\play-services-basement-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "120,145", "startColumns": "4,4", "startOffsets": "7352,9544", "endColumns": "67,166", "endOffsets": "7415,9706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\2bc77b08baa5e058442f01d3b8740064\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "123", "startColumns": "4", "startOffsets": "7557", "endColumns": "82", "endOffsets": "7635"}}, {"source": "D:\\abonelik_sistemi_final (3)\\abonelik_sistemi_final (2)\\app\\build\\generated\\res\\processReleaseGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,586", "endColumns": "81,103,108,119,115,84", "endOffsets": "132,236,345,465,581,666"}, "to": {"startLines": "170,171,172,173,174,244", "startColumns": "4,4,4,4,4,4", "startOffsets": "12008,12090,12194,12303,12423,17461", "endColumns": "81,103,108,119,115,84", "endOffsets": "12085,12189,12298,12418,12534,17541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ddf931afad622e529ec9d528ae33174\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "2,7,8,10,11,20,21,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,92,93,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,121,125,126,127,128,129,130,131,253,272,273,277,278,282,284,285,293,299,309,342,372,405", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,470,542,672,737,1474,1543,1879,1949,2017,2089,2159,2220,2294,2367,2428,2489,2551,2615,2677,2738,2806,2906,2966,3032,3105,3174,3231,3283,3345,3417,3493,5872,5907,6169,6224,6287,6342,6400,6458,6519,6582,6639,6690,6740,6801,6858,6924,6958,6993,7420,7696,7763,7835,7904,7973,8047,8119,18044,19019,19136,19337,19447,19648,19871,19943,20314,20517,20818,22549,23549,24231", "endLines": "2,7,8,10,11,20,21,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,92,93,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,121,125,126,127,128,129,130,131,253,272,276,277,281,282,284,285,298,308,341,362,404,410", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "205,537,625,732,798,1538,1601,1944,2012,2084,2154,2215,2289,2362,2423,2484,2546,2610,2672,2733,2801,2901,2961,3027,3100,3169,3226,3278,3340,3412,3488,3553,5902,5937,6219,6282,6337,6395,6453,6514,6577,6634,6685,6735,6796,6853,6919,6953,6988,7023,7485,7758,7830,7899,7968,8042,8114,8202,18110,19131,19332,19442,19643,19772,19938,20005,20512,20813,22544,23225,24226,24393"}}, {"source": "D:\\abonelik_sistemi_final (3)\\abonelik_sistemi_final (2)\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "9,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "630,1606,1653,1700,1747,1792,1837", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "667,1648,1695,1742,1787,1832,1874"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\758d06e28d876accf8cd86f1d399c64f\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,89,90,119,135,136,156,157,158,175,176,239,241,242,243,245,246,248,255,256,257,262,265,268", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3558,3617,3676,3736,3796,3856,3916,3976,4036,4096,4156,4216,4276,4335,4395,4455,4515,4575,4635,4695,4755,4815,4875,4935,4994,5054,5114,5173,5232,5291,5350,5409,5468,5542,5600,5712,5763,7299,8385,8450,10902,10968,11069,12539,12591,17137,17337,17391,17427,17546,17596,17720,18166,18213,18249,18601,18713,18824", "endLines": "53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,89,90,119,135,136,156,157,158,175,176,239,241,242,243,245,246,248,255,256,257,264,267,271", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "3612,3671,3731,3791,3851,3911,3971,4031,4091,4151,4211,4271,4330,4390,4450,4510,4570,4630,4690,4750,4810,4870,4930,4989,5049,5109,5168,5227,5286,5345,5404,5463,5537,5595,5650,5758,5813,7347,8445,8499,10963,11064,11122,12586,12646,17194,17386,17422,17456,17591,17645,17761,18208,18244,18334,18708,18819,19014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\7b9074ea43515050688821caed3e085a\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "114", "startColumns": "4", "startOffsets": "7028", "endColumns": "42", "endOffsets": "7066"}}]}]}