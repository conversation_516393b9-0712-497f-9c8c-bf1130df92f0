package com.example.abonekaptanmobile.data.local.dao

import androidx.room.*
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity
import kotlinx.coroutines.flow.Flow

/**
 * Turkish: <PERSON><PERSON> aşamalı analiz sonuçları için DAO.
 * English: DAO for two-stage analysis results.
 */
@Dao
interface StageAnalysisDao {

    /**
     * Turkish: Tüm analiz sonuçlarını getir.
     * English: Get all analysis results.
     */
    @Query("SELECT * FROM stage_analysis ORDER BY createdAt DESC")
    fun getAllAnalysisResults(): Flow<List<StageAnalysisEntity>>

    /**
     * Turkish: Aşama 1'i tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 1.
     */
    @Query("SELECT * FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 AND stage1_companyConfidence >= :minConfidence ORDER BY date ASC")
    fun getStage1CompletedEmails(minConfidence: Float = 0.6f): Flow<List<StageAnalysisEntity>>

    /**
     * Turkish: Aşama 2'ye geçecek e-postaları getir.
     * English: Get emails ready for stage 2.
     */
    @Query("SELECT * FROM stage_analysis WHERE stage1_isSubscriptionCompany = 1 AND stage1_companyConfidence >= :minConfidence AND processingStatus = 'STAGE1_COMPLETED' ORDER BY date ASC")
    fun getEmailsForStage2(minConfidence: Float = 0.6f): Flow<List<StageAnalysisEntity>>

    /**
     * Turkish: Aşama 2'yi tamamlanmış e-postaları getir.
     * English: Get emails that completed stage 2.
     */
    @Query("SELECT * FROM stage_analysis WHERE processingStatus = 'STAGE2_COMPLETED' ORDER BY date ASC")
    fun getStage2CompletedEmails(): Flow<List<StageAnalysisEntity>>

    /**
     * Turkish: Belirli bir şirketin analiz sonuçlarını getir.
     * English: Get analysis results for a specific company.
     */
    @Query("SELECT * FROM stage_analysis WHERE stage1_companyName = :companyName ORDER BY date ASC")
    fun getAnalysisResultsByCompany(companyName: String): Flow<List<StageAnalysisEntity>>

    /**
     * Turkish: Analiz sonucu ekle.
     * English: Insert analysis result.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAnalysisResult(result: StageAnalysisEntity): Long

    /**
     * Turkish: Birden fazla analiz sonucu ekle.
     * English: Insert multiple analysis results.
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAnalysisResults(results: List<StageAnalysisEntity>)

    /**
     * Turkish: Aşama 2 sonuçlarını güncelle.
     * English: Update stage 2 results.
     */
    @Query("""
        UPDATE stage_analysis 
        SET stage2_emailType = :emailType,
            stage2_emailTypeConfidence = :confidence,
            stage2_timestamp = :timestamp,
            stage2_rawContent = :rawContent,
            processingStatus = 'STAGE2_COMPLETED'
        WHERE id = :id
    """)
    suspend fun updateStage2Results(
        id: Long,
        emailType: String,
        confidence: Float,
        timestamp: Long,
        rawContent: String
    )

    /**
     * Turkish: İşlem durumunu güncelle.
     * English: Update processing status.
     */
    @Query("UPDATE stage_analysis SET processingStatus = :status WHERE id = :id")
    suspend fun updateProcessingStatus(id: Long, status: String)

    /**
     * Turkish: Belirli bir e-posta ID'sine göre analiz sonucunu getir.
     * English: Get analysis result by email ID.
     */
    @Query("SELECT * FROM stage_analysis WHERE emailId = :emailId LIMIT 1")
    suspend fun getAnalysisResultByEmailId(emailId: String): StageAnalysisEntity?

    /**
     * Turkish: Tüm analiz sonuçlarını sil.
     * English: Delete all analysis results.
     */
    @Query("DELETE FROM stage_analysis")
    suspend fun deleteAllAnalysisResults()

    /**
     * Turkish: Belirli bir tarihten eski analiz sonuçlarını sil.
     * English: Delete analysis results older than a specific date.
     */
    @Query("DELETE FROM stage_analysis WHERE createdAt < :timestamp")
    suspend fun deleteOldAnalysisResults(timestamp: Long)

    /**
     * Turkish: İstatistikler için özet bilgi getir.
     * English: Get summary information for statistics.
     */
    @Query("""
        SELECT 
            COUNT(*) as totalEmails,
            SUM(CASE WHEN stage1_isSubscriptionCompany = 1 THEN 1 ELSE 0 END) as stage1Passed,
            SUM(CASE WHEN processingStatus = 'STAGE2_COMPLETED' THEN 1 ELSE 0 END) as stage2Completed,
            AVG(stage1_companyConfidence) as avgConfidence
        FROM stage_analysis
    """)
    suspend fun getAnalysisStatistics(): AnalysisStatistics

    /**
     * Turkish: Şirket bazında istatistikler.
     * English: Company-based statistics.
     */
    @Query("""
        SELECT 
            stage1_companyName as companyName,
            COUNT(*) as emailCount,
            AVG(stage1_companyConfidence) as avgConfidence,
            SUM(CASE WHEN processingStatus = 'STAGE2_COMPLETED' THEN 1 ELSE 0 END) as completedCount
        FROM stage_analysis 
        WHERE stage1_isSubscriptionCompany = 1 
        GROUP BY stage1_companyName 
        ORDER BY emailCount DESC
    """)
    suspend fun getCompanyStatistics(): List<CompanyStatistics>
}

/**
 * Turkish: Analiz istatistikleri için data class.
 * English: Data class for analysis statistics.
 */
data class AnalysisStatistics(
    val totalEmails: Int,
    val stage1Passed: Int,
    val stage2Completed: Int,
    val avgConfidence: Double
)

/**
 * Turkish: Şirket istatistikleri için data class.
 * English: Data class for company statistics.
 */
data class CompanyStatistics(
    val companyName: String,
    val emailCount: Int,
    val avgConfidence: Double,
    val completedCount: Int
)
