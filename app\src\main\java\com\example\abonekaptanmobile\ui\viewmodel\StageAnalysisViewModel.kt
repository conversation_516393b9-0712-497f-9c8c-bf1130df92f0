package com.example.abonekaptanmobile.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.abonekaptanmobile.data.local.dao.AnalysisStatistics
import com.example.abonekaptanmobile.data.local.entity.StageAnalysisEntity
import com.example.abonekaptanmobile.data.repository.StageAnalysisRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * Turkish: <PERSON><PERSON> aşamalı analiz sonuçları için ViewModel.
 * English: ViewModel for two-stage analysis results.
 */
@HiltViewModel
class StageAnalysisViewModel @Inject constructor(
    private val stageAnalysisRepository: StageAnalysisRepository
) : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _statistics = MutableStateFlow<AnalysisStatistics?>(null)
    val statistics: StateFlow<AnalysisStatistics?> = _statistics.asStateFlow()

    // Tüm analiz sonuçları
    val analysisResults: StateFlow<List<StageAnalysisEntity>> = 
        stageAnalysisRepository.getAllAnalysisResults()
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = emptyList()
            )

    // Aşama 1'i tamamlayan e-postalar
    val stage1CompletedEmails: StateFlow<List<StageAnalysisEntity>> = 
        stageAnalysisRepository.getStage1CompletedEmails()
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = emptyList()
            )

    // Aşama 2'ye hazır e-postalar
    val emailsForStage2: StateFlow<List<StageAnalysisEntity>> = 
        stageAnalysisRepository.getEmailsForStage2()
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = emptyList()
            )

    // Aşama 2'yi tamamlayan e-postalar
    val stage2CompletedEmails: StateFlow<List<StageAnalysisEntity>> = 
        stageAnalysisRepository.getStage2CompletedEmails()
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = emptyList()
            )

    init {
        loadStatistics()
    }

    /**
     * Turkish: İstatistikleri yükle.
     * English: Load statistics.
     */
    fun loadStatistics() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                val stats = stageAnalysisRepository.getAnalysisStatistics()
                _statistics.value = stats
                
            } catch (e: Exception) {
                _error.value = "İstatistikler yüklenirken hata oluştu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Turkish: Belirli bir şirketin analiz sonuçlarını getir.
     * English: Get analysis results for a specific company.
     */
    fun getAnalysisResultsByCompany(companyName: String): StateFlow<List<StageAnalysisEntity>> {
        return stageAnalysisRepository.getAnalysisResultsByCompany(companyName)
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = emptyList()
            )
    }

    /**
     * Turkish: Tüm analiz sonuçlarını sil.
     * English: Delete all analysis results.
     */
    fun deleteAllAnalysisResults() {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                stageAnalysisRepository.deleteAllAnalysisResults()
                loadStatistics() // İstatistikleri yenile
                
            } catch (e: Exception) {
                _error.value = "Analiz sonuçları silinirken hata oluştu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Turkish: Eski analiz sonuçlarını sil.
     * English: Delete old analysis results.
     */
    fun deleteOldAnalysisResults(daysOld: Int = 30) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                stageAnalysisRepository.deleteOldAnalysisResults(daysOld)
                loadStatistics() // İstatistikleri yenile
                
            } catch (e: Exception) {
                _error.value = "Eski analiz sonuçları silinirken hata oluştu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Turkish: Hatayı temizle.
     * English: Clear error.
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Turkish: Şirket bazında istatistikleri getir.
     * English: Get company-based statistics.
     */
    fun getCompanyStatistics(): Flow<List<com.example.abonekaptanmobile.data.local.dao.CompanyStatistics>> {
        return flow {
            try {
                val stats = stageAnalysisRepository.getCompanyStatistics()
                emit(stats)
            } catch (e: Exception) {
                _error.value = "Şirket istatistikleri alınırken hata oluştu: ${e.message}"
                emit(emptyList())
            }
        }
    }

    /**
     * Turkish: Analiz sonuçlarını yenile.
     * English: Refresh analysis results.
     */
    fun refreshData() {
        loadStatistics()
    }

    /**
     * Turkish: Belirli bir e-posta ID'sine göre analiz sonucunu getir.
     * English: Get analysis result by email ID.
     */
    suspend fun getAnalysisResultByEmailId(emailId: String): StageAnalysisEntity? {
        return try {
            stageAnalysisRepository.getAnalysisResultByEmailId(emailId)
        } catch (e: Exception) {
            _error.value = "E-posta analiz sonucu alınırken hata oluştu: ${e.message}"
            null
        }
    }

    /**
     * Turkish: Aşama 2 sonucunu güncelle.
     * English: Update stage 2 result.
     */
    fun updateStage2Result(
        entityId: Long,
        emailType: String,
        confidence: Float,
        rawContent: String
    ) {
        viewModelScope.launch {
            try {
                _isLoading.value = true
                _error.value = null
                
                stageAnalysisRepository.updateStage2Result(
                    entityId = entityId,
                    emailType = emailType,
                    confidence = confidence,
                    rawContent = rawContent
                )
                
                loadStatistics() // İstatistikleri yenile
                
            } catch (e: Exception) {
                _error.value = "Aşama 2 sonucu güncellenirken hata oluştu: ${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Turkish: İşlem durumunu güncelle.
     * English: Update processing status.
     */
    fun updateProcessingStatus(entityId: Long, status: String) {
        viewModelScope.launch {
            try {
                stageAnalysisRepository.updateProcessingStatus(entityId, status)
                loadStatistics() // İstatistikleri yenile
            } catch (e: Exception) {
                _error.value = "İşlem durumu güncellenirken hata oluştu: ${e.message}"
            }
        }
    }

    /**
     * Turkish: Filtrelenmiş sonuçları getir.
     * English: Get filtered results.
     */
    fun getFilteredResults(
        companyName: String? = null,
        processingStatus: String? = null,
        minConfidence: Float? = null
    ): Flow<List<StageAnalysisEntity>> {
        return analysisResults.map { results ->
            results.filter { result ->
                val companyMatch = companyName?.let { 
                    result.stage1_companyName.contains(it, ignoreCase = true) 
                } ?: true
                
                val statusMatch = processingStatus?.let { 
                    result.processingStatus == it 
                } ?: true
                
                val confidenceMatch = minConfidence?.let { 
                    result.stage1_companyConfidence >= it 
                } ?: true
                
                companyMatch && statusMatch && confidenceMatch
            }
        }
    }

    /**
     * Turkish: Özet bilgileri getir.
     * English: Get summary information.
     */
    fun getSummaryInfo(): Flow<Map<String, Int>> {
        return analysisResults.map { results ->
            mapOf(
                "total" to results.size,
                "stage1_passed" to results.count { it.stage1_isSubscriptionCompany },
                "stage2_completed" to results.count { it.processingStatus == "STAGE2_COMPLETED" },
                "failed" to results.count { it.processingStatus.contains("FAILED") }
            )
        }
    }
}
