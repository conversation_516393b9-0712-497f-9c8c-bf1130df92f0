// Generated by <PERSON><PERSON> (https://dagger.dev).
package com.example.abonekaptanmobile.data.repository;

import com.example.abonekaptanmobile.data.local.dao.StageAnalysisDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class StageAnalysisRepository_Factory implements Factory<StageAnalysisRepository> {
  private final Provider<StageAnalysisDao> stageAnalysisDaoProvider;

  public StageAnalysisRepository_Factory(Provider<StageAnalysisDao> stageAnalysisDaoProvider) {
    this.stageAnalysisDaoProvider = stageAnalysisDaoProvider;
  }

  @Override
  public StageAnalysisRepository get() {
    return newInstance(stageAnalysisDaoProvider.get());
  }

  public static StageAnalysisRepository_Factory create(
      Provider<StageAnalysisDao> stageAnalysisDaoProvider) {
    return new StageAnalysisRepository_Factory(stageAnalysisDaoProvider);
  }

  public static StageAnalysisRepository newInstance(StageAnalysisDao stageAnalysisDao) {
    return new StageAnalysisRepository(stageAnalysisDao);
  }
}
