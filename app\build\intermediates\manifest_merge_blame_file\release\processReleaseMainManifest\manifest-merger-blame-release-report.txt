1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.abonekaptanmobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" /> <!-- BU SATIRI EKLEDİK/DÜZELTTİK -->
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:6:5-67
11-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:6:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:7:5-79
12-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:7:22-76
13    <!--
14         GET_ACCOUNTS is not strictly needed for Google Sign-In on newer APIs,
15         but can be useful for account discovery on older devices.
16         The Google Sign-In library handles most of this.
17    -->
18    <uses-permission android:name="android.permission.GET_ACCOUNTS" />
18-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:11:5-71
18-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:11:22-68
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:12:5-68
19-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:12:22-65
20    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
20-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:5-81
20-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:25:22-78
21    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
21-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:5-77
21-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:26:22-74
22
23    <permission
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
24        android:name="com.example.abonekaptanmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
25        android:protectionLevel="signature" />
25-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
26
27    <uses-permission android:name="com.example.abonekaptanmobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
27-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
28
29    <application
29-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:14:5-54:19
30        android:name="com.example.abonekaptanmobile.AboneKaptanApp"
30-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:15:9-39
31        android:allowBackup="true"
31-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:16:9-35
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0ddf931afad622e529ec9d528ae33174\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
33        android:dataExtractionRules="@xml/data_extraction_rules"
33-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:17:9-65
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:18:9-54
36        android:icon="@mipmap/ic_launcher"
36-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:19:9-43
37        android:label="@string/app_name"
37-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:20:9-41
38        android:networkSecurityConfig="@xml/network_security_config"
38-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:24:9-69
39        android:roundIcon="@mipmap/ic_launcher_round"
39-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:21:9-54
40        android:supportsRtl="true"
40-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:22:9-35
41        android:theme="@style/Theme.AboneKaptanMobile"
41-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:23:9-55
42        android:usesCleartextTraffic="true" >
42-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:25:9-44
43        <activity
43-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:27:9-40:20
44            android:name="com.example.abonekaptanmobile.MainActivity"
44-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:28:13-41
45            android:exported="true"
45-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:29:13-36
46            android:label="@string/app_name"
46-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:30:13-45
47            android:theme="@style/Theme.AboneKaptanMobile" >
47-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:31:13-59
48            <intent-filter>
48-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:32:13-35:29
49                <action android:name="android.intent.action.MAIN" />
49-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:33:17-69
49-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:33:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:34:17-77
51-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:34:27-74
52            </intent-filter>
53            <!--
54                 Add this intent filter for Google Sign-In redirect, if needed by specific configurations
55                 Usually, Google Sign-In library handles this without explicit redirect URI in manifest for mobile.
56                 This is more common for web OAuth flows. For mobile, ensure your SHA-1 is in Firebase/Google Cloud.
57            -->
58        </activity>
59
60        <!-- WorkManager Initializer -->
61        <provider
62            android:name="androidx.startup.InitializationProvider"
62-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:44:13-67
63            android:authorities="com.example.abonekaptanmobile.androidx-startup"
63-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:45:13-68
64            android:exported="false" >
64-->D:\abonelik_sistemi_final (3)\abonelik_sistemi_final (2)\app\src\main\AndroidManifest.xml:46:13-37
65            <meta-data
65-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.emoji2.text.EmojiCompatInitializer"
66-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
67                android:value="androidx.startup" />
67-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7f93d72572c8f1eb0463681a76a9f82c\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
69-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
70                android:value="androidx.startup" />
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4f20881ad2ecbf951bde9e37fb1a34d4\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
73                android:value="androidx.startup" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
74        </provider>
75
76        <activity
76-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
77            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
77-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
78            android:excludeFromRecents="true"
78-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
79            android:exported="false"
79-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
80            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
80-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
81        <!--
82            Service handling Google Sign-In user revocation. For apps that do not integrate with
83            Google Sign-In, this service will never be started.
84        -->
85        <service
85-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
86            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
86-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
87            android:exported="true"
87-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
88            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
88-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
89            android:visibleToInstantApps="true" />
89-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a2736db7b19985fe272b12b7e5c308cb\transformed\play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
90
91        <activity
91-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
92            android:name="com.google.android.gms.common.api.GoogleApiActivity"
92-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
93            android:exported="false"
93-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
94            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
94-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\44e19e1f9b56976b7f9e33316bf0ba7e\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
95
96        <meta-data
96-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
97            android:name="com.google.android.gms.version"
97-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
98            android:value="@integer/google_play_services_version" />
98-->[com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f17758470c37236a8702f22be0817404\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
99
100        <service
100-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:39:9-45:35
101            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
101-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:40:13-88
102            android:directBootAware="false"
102-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:41:13-44
103            android:enabled="@bool/enable_system_alarm_service_default"
103-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:42:13-72
104            android:exported="false" />
104-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:43:13-37
105        <service
105-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:46:9-52:35
106            android:name="androidx.work.impl.background.systemjob.SystemJobService"
106-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:47:13-84
107            android:directBootAware="false"
107-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:48:13-44
108            android:enabled="@bool/enable_system_job_service_default"
108-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:49:13-70
109            android:exported="true"
109-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:50:13-36
110            android:permission="android.permission.BIND_JOB_SERVICE" />
110-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:51:13-69
111        <service
111-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:53:9-59:35
112            android:name="androidx.work.impl.foreground.SystemForegroundService"
112-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:54:13-81
113            android:directBootAware="false"
113-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:55:13-44
114            android:enabled="@bool/enable_system_foreground_service_default"
114-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:56:13-77
115            android:exported="false" />
115-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:57:13-37
116
117        <receiver
117-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:61:9-66:35
118            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
118-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:62:13-88
119            android:directBootAware="false"
119-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:63:13-44
120            android:enabled="true"
120-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:64:13-35
121            android:exported="false" />
121-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:65:13-37
122        <receiver
122-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:67:9-77:20
123            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
123-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:68:13-106
124            android:directBootAware="false"
124-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:69:13-44
125            android:enabled="false"
125-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:70:13-36
126            android:exported="false" >
126-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:71:13-37
127            <intent-filter>
127-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:73:13-76:29
128                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:17-87
128-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:74:25-84
129                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:17-90
129-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:75:25-87
130            </intent-filter>
131        </receiver>
132        <receiver
132-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:78:9-88:20
133            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
133-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:79:13-104
134            android:directBootAware="false"
134-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:80:13-44
135            android:enabled="false"
135-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:81:13-36
136            android:exported="false" >
136-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:82:13-37
137            <intent-filter>
137-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:84:13-87:29
138                <action android:name="android.intent.action.BATTERY_OKAY" />
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:17-77
138-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:85:25-74
139                <action android:name="android.intent.action.BATTERY_LOW" />
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:17-76
139-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:86:25-73
140            </intent-filter>
141        </receiver>
142        <receiver
142-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:89:9-99:20
143            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
143-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:90:13-104
144            android:directBootAware="false"
144-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:91:13-44
145            android:enabled="false"
145-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:92:13-36
146            android:exported="false" >
146-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:93:13-37
147            <intent-filter>
147-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:95:13-98:29
148                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:17-83
148-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:96:25-80
149                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:17-82
149-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:97:25-79
150            </intent-filter>
151        </receiver>
152        <receiver
152-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:100:9-109:20
153            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
153-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:101:13-103
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:102:13-44
155            android:enabled="false"
155-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:103:13-36
156            android:exported="false" >
156-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:104:13-37
157            <intent-filter>
157-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:106:13-108:29
158                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:17-79
158-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:107:25-76
159            </intent-filter>
160        </receiver>
161        <receiver
161-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:110:9-121:20
162            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
162-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:111:13-88
163            android:directBootAware="false"
163-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:112:13-44
164            android:enabled="false"
164-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:113:13-36
165            android:exported="false" >
165-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:114:13-37
166            <intent-filter>
166-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:116:13-120:29
167                <action android:name="android.intent.action.BOOT_COMPLETED" />
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:17-79
167-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:117:25-76
168                <action android:name="android.intent.action.TIME_SET" />
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:17-73
168-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:118:25-70
169                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:17-81
169-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:119:25-78
170            </intent-filter>
171        </receiver>
172        <receiver
172-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:122:9-131:20
173            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
173-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:123:13-99
174            android:directBootAware="false"
174-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:124:13-44
175            android:enabled="@bool/enable_system_alarm_service_default"
175-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:125:13-72
176            android:exported="false" >
176-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:126:13-37
177            <intent-filter>
177-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:128:13-130:29
178                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:17-98
178-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:129:25-95
179            </intent-filter>
180        </receiver>
181        <receiver
181-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:132:9-142:20
182            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
182-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:133:13-78
183            android:directBootAware="false"
183-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:134:13-44
184            android:enabled="true"
184-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:135:13-35
185            android:exported="true"
185-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:136:13-36
186            android:permission="android.permission.DUMP" >
186-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:137:13-57
187            <intent-filter>
187-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:139:13-141:29
188                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:17-88
188-->[androidx.work:work-runtime:2.9.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7ac341cec7d42871491bf80bed891004\transformed\work-runtime-2.9.0\AndroidManifest.xml:140:25-85
189            </intent-filter>
190        </receiver>
191
192        <service
192-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
193            android:name="androidx.room.MultiInstanceInvalidationService"
193-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
194            android:directBootAware="true"
194-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
195            android:exported="false" />
195-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\29f7d0d13b3d445bb9dc84bbc73ccbf0\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
196
197        <receiver
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
198            android:name="androidx.profileinstaller.ProfileInstallReceiver"
198-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
199            android:directBootAware="false"
199-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
200            android:enabled="true"
200-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
201            android:exported="true"
201-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
202            android:permission="android.permission.DUMP" >
202-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
203            <intent-filter>
203-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
204                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
204-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
205            </intent-filter>
206            <intent-filter>
206-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
207                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
207-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
208            </intent-filter>
209            <intent-filter>
209-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
210                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
210-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
211            </intent-filter>
212            <intent-filter>
212-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
213                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
213-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8723ba3e89b3f0a40781a4eda4071e28\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
214            </intent-filter>
215        </receiver>
216    </application>
217
218</manifest>
