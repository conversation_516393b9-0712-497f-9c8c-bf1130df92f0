package com.example.abonekaptanmobile.ui.screens;

import androidx.compose.foundation.layout.*;
import androidx.compose.material.icons.Icons;
import androidx.compose.material3.*;
import androidx.compose.runtime.*;
import androidx.compose.ui.Alignment;
import androidx.compose.ui.Modifier;
import androidx.compose.ui.text.font.FontWeight;
import androidx.compose.ui.text.style.TextOverflow;
import com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult;
import com.example.abonekaptanmobile.ui.viewmodel.Stage1AnalysisViewModel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u00008\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010$\n\u0000\u001a\u0010\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u001a \u0010\u0004\u001a\u00020\u00012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u00062\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u001a*\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u000b2\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u000e\u001a\u00020\u000fH\u0003\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u0010\u0010\u0011\u001a\u001c\u0010\u0012\u001a\u00020\u00012\u0012\u0010\u0013\u001a\u000e\u0012\u0004\u0012\u00020\u000b\u0012\u0004\u0012\u00020\r0\u0014H\u0003\u0082\u0002\u0007\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\u0015"}, d2 = {"AnalysisResultCard", "", "result", "Lcom/example/abonekaptanmobile/data/local/entity/Stage1AnalysisResult;", "Stage1AnalysisScreen", "onNavigateBack", "Lkotlin/Function0;", "viewModel", "Lcom/example/abonekaptanmobile/ui/viewmodel/Stage1AnalysisViewModel;", "StatisticItem", "label", "", "count", "", "color", "Landroidx/compose/ui/graphics/Color;", "StatisticItem-mxwnekA", "(Ljava/lang/String;IJ)V", "StatisticsCard", "statistics", "", "app_debug"})
public final class Stage1AnalysisScreenKt {
    
    /**
     * Turkish: Aşama 1 analiz sonuçlarını gösteren ekran
     * English: Screen showing Stage 1 analysis results
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void Stage1AnalysisScreen(@org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function0<kotlin.Unit> onNavigateBack, @org.jetbrains.annotations.NotNull()
    com.example.abonekaptanmobile.ui.viewmodel.Stage1AnalysisViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatisticsCard(java.util.Map<java.lang.String, java.lang.Integer> statistics) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AnalysisResultCard(com.example.abonekaptanmobile.data.local.entity.Stage1AnalysisResult result) {
    }
}