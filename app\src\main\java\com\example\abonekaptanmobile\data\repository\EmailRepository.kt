package com.example.abonekaptanmobile.data.repository

import com.example.abonekaptanmobile.data.local.dao.EmailDao
import com.example.abonekaptanmobile.data.local.entity.EmailEntity
import com.example.abonekaptanmobile.model.RawEmail
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Turkish: E-posta veritabanı işlemleri için repository.
 * English: Repository for email database operations.
 */
@Singleton
class EmailRepository @Inject constructor(
    private val emailDao: EmailDao
) {

    /**
     * Turkish: E-postaları veritabanına kaydet.
     * English: Insert emails into database.
     */
    suspend fun insertEmails(emails: List<RawEmail>) {
        val emailEntities = emails.map { rawEmail ->
            EmailEntity(
                id = rawEmail.id,
                from = rawEmail.from,
                subject = rawEmail.subject,
                snippet = rawEmail.snippet,
                bodyPlainText = rawEmail.bodyPlainText,
                bodySnippet = rawEmail.bodySnippet,
                date = rawEmail.date,
                threadId = rawEmail.threadId,
                labelIds = rawEmail.labels.joinToString(",")
            )
        }
        emailDao.insertEmails(emailEntities)
    }

    /**
     * Turkish: Tüm e-postaları getir.
     * English: Get all emails.
     */
    fun getAllEmails(): Flow<List<EmailEntity>> {
        return emailDao.getAllEmails()
    }

    /**
     * Turkish: E-posta sayısını getir.
     * English: Get email count.
     */
    suspend fun getEmailCount(): Int {
        return emailDao.getEmailCount()
    }

    /**
     * Turkish: Tüm e-postaları sil.
     * English: Delete all emails.
     */
    suspend fun deleteAllEmails() {
        emailDao.deleteAllEmails()
    }
}
