{"logs": [{"outputFile": "com.example.abonekaptanmobile.app-mergeReleaseResources-63:/values-b+sr+Latn/values-b+sr+Latn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\e1062e727b039c203a5f49af9a364889\\transformed\\material3-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,289,417,534,633,727,838,974,1094,1236,1321,1421,1516,1614,1730,1855,1960,2101,2241,2374,2554,2679,2799,2924,3046,3142,3240,3358,3488,3588,3690,3799,3941,4090,4199,4302,4379,4478,4576,4665,4751,4858,4938,5021,5118,5221,5314,5412,5499,5607,5704,5806,5939,6019,6128", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "167,284,412,529,628,722,833,969,1089,1231,1316,1416,1511,1609,1725,1850,1955,2096,2236,2369,2549,2674,2794,2919,3041,3137,3235,3353,3483,3583,3685,3794,3936,4085,4194,4297,4374,4473,4571,4660,4746,4853,4933,5016,5113,5216,5309,5407,5494,5602,5699,5801,5934,6014,6123,6222"}, "to": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3642,3759,3876,4004,4121,4220,4314,4425,4561,4681,4823,4908,5008,5103,5201,5317,5442,5547,5688,5828,5961,6141,6266,6386,6511,6633,6729,6827,6945,7075,7175,7277,7386,7528,7677,7786,7889,7966,8065,8163,8252,8338,8445,8525,8608,8705,8808,8901,8999,9086,9194,9291,9393,9526,9606,9715", "endColumns": "116,116,127,116,98,93,110,135,119,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,117,129,99,101,108,141,148,108,102,76,98,97,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,108,98", "endOffsets": "3754,3871,3999,4116,4215,4309,4420,4556,4676,4818,4903,5003,5098,5196,5312,5437,5542,5683,5823,5956,6136,6261,6381,6506,6628,6724,6822,6940,7070,7170,7272,7381,7523,7672,7781,7884,7961,8060,8158,8247,8333,8440,8520,8603,8700,8803,8896,8994,9081,9189,9286,9388,9521,9601,9710,9809"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\f17758470c37236a8702f22be0817404\\transformed\\play-services-basement-18.2.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2012", "endColumns": "129", "endOffsets": "2137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\0ddf931afad622e529ec9d528ae33174\\transformed\\core-1.12.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,97", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "105,203,305,402,506,610,715,10372", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "198,300,397,501,605,710,826,10468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\ae9863232b9716e1e6ec45d64efe02de\\transformed\\foundation-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "101,102", "startColumns": "4,4", "startOffsets": "10740,10830", "endColumns": "89,91", "endOffsets": "10825,10917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\758d06e28d876accf8cd86f1d399c64f\\transformed\\ui-release\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,990,1061,1141,1226,1299,1378,1448", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,985,1056,1136,1221,1294,1373,1443,1561"}, "to": {"startLines": "9,10,29,30,31,32,33,90,91,92,93,94,95,96,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "831,928,3190,3287,3388,3474,3551,9814,9906,9991,10063,10134,10214,10299,10473,10552,10622", "endColumns": "96,86,96,100,85,76,90,91,84,71,70,79,84,72,78,69,117", "endOffsets": "923,1010,3282,3383,3469,3546,3637,9901,9986,10058,10129,10209,10294,10367,10547,10617,10735"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\44e19e1f9b56976b7f9e33316bf0ba7e\\transformed\\play-services-base-18.0.1\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1015,1122,1280,1407,1517,1671,1798,1910,2142,2291,2398,2558,2685,2834,2977,3045,3110", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "1117,1275,1402,1512,1666,1793,1905,2007,2286,2393,2553,2680,2829,2972,3040,3105,3185"}}]}]}